{"name": "utah-treasure-finder", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "recharts": "^2.8.0", "lucide-react": "^0.263.1", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "sqlite3": "^5.1.6", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "axios": "^1.5.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vite": "^4.4.5", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.27"}}