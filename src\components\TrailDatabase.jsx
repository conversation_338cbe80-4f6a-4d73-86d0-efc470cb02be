import React, { useState } from 'react';
import { useTrails } from '../context/TrailContext';
import { getConfidenceLevel, getScoreColor } from '../utils/scoringAlgorithm';
import { Mountain, Clock, MapPin, TrendingUp, Filter, Download, Search } from 'lucide-react';

function TrailDatabase() {
  const { filteredTrails, filters, updateFilters, sortBy, setSortBy, resetFilters } = useTrails();
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const filteredBySearch = filteredTrails.filter(trail =>
    trail.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    trail.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
    trail.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const exportData = () => {
    const csvContent = [
      ['Name', 'Location', 'Score', 'Confidence', 'Elevation', 'Difficulty', 'Hike Time', 'Length'].join(','),
      ...filteredBySearch.map(trail => [
        trail.name,
        trail.location,
        trail.totalScore.toFixed(1),
        getConfidenceLevel(trail.totalScore),
        trail.elevation,
        trail.difficulty,
        trail.hikeTime,
        trail.length
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'utah-trails-analysis.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Trail Database</h1>
          <p className="text-gray-600">Comprehensive Utah trail analysis with treasure likelihood scoring</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={exportData}
            className="btn-secondary flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Export CSV</span>
          </button>
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn-primary flex items-center space-x-2"
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
          </button>
        </div>
      </div>

      {/* Search and Sort Controls */}
      <div className="card">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search trails by name, location, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pl-10"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sort by:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="input-field"
              >
                <option value="score">Treasure Score</option>
                <option value="name">Trail Name</option>
                <option value="elevation">Elevation</option>
                <option value="difficulty">Difficulty</option>
              </select>
            </div>
            
            <div className="flex items-end">
              <button
                onClick={resetFilters}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md"
              >
                Reset Filters
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Filters */}
      {showFilters && (
        <div className="card bg-gray-50">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Advanced Filters</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
              <select
                value={filters.difficulty}
                onChange={(e) => updateFilters({ difficulty: e.target.value })}
                className="input-field"
              >
                <option value="all">All Difficulties</option>
                <option value="Easy">Easy</option>
                <option value="Moderate">Moderate</option>
                <option value="Hard">Hard</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Region</label>
              <select
                value={filters.region}
                onChange={(e) => updateFilters({ region: e.target.value })}
                className="input-field"
              >
                <option value="all">All Regions</option>
                <option value="salt-lake">Salt Lake Area</option>
                <option value="utah-county">Utah County</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Score Range: {filters.scoreRange[0]} - {filters.scoreRange[1]}
              </label>
              <div className="flex space-x-2">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={filters.scoreRange[0]}
                  onChange={(e) => updateFilters({ 
                    scoreRange: [parseInt(e.target.value), filters.scoreRange[1]] 
                  })}
                  className="flex-1"
                />
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={filters.scoreRange[1]}
                  onChange={(e) => updateFilters({ 
                    scoreRange: [filters.scoreRange[0], parseInt(e.target.value)] 
                  })}
                  className="flex-1"
                />
              </div>
            </div>
            
            <div className="flex items-center">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={filters.wasatchFrontOnly}
                  onChange={(e) => updateFilters({ wasatchFrontOnly: e.target.checked })}
                  className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
                />
                <span className="text-sm text-gray-700">Wasatch Front Only</span>
              </label>
            </div>
          </div>
        </div>
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-gray-600">
        <span>Showing {filteredBySearch.length} of {filteredTrails.length} trails</span>
        <span>Sorted by {sortBy}</span>
      </div>

      {/* Trail Cards Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredBySearch.map((trail) => (
          <div key={trail.id} className="card hover:shadow-lg transition-shadow duration-200">
            {/* Trail Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-900 mb-1">{trail.name}</h3>
                <p className="text-gray-600 flex items-center">
                  <MapPin className="w-4 h-4 mr-1" />
                  {trail.location}
                </p>
              </div>
              
              <div className="text-right">
                <div className={`px-3 py-1 rounded-full text-sm font-bold ${getScoreColor(trail.totalScore)}`}>
                  {trail.totalScore.toFixed(1)}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {getConfidenceLevel(trail.totalScore)}
                </p>
              </div>
            </div>

            {/* Trail Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <Mountain className="w-5 h-5 mx-auto text-gray-500 mb-1" />
                <p className="text-sm font-medium text-gray-900">{trail.elevation}ft</p>
                <p className="text-xs text-gray-500">Elevation</p>
              </div>
              
              <div className="text-center">
                <TrendingUp className="w-5 h-5 mx-auto text-gray-500 mb-1" />
                <p className="text-sm font-medium text-gray-900">{trail.elevationGain}ft</p>
                <p className="text-xs text-gray-500">Gain</p>
              </div>
              
              <div className="text-center">
                <Clock className="w-5 h-5 mx-auto text-gray-500 mb-1" />
                <p className="text-sm font-medium text-gray-900">{trail.hikeTime}min</p>
                <p className="text-xs text-gray-500">Hike Time</p>
              </div>
              
              <div className="text-center">
                <span className={`inline-block w-3 h-3 rounded-full mb-1 ${
                  trail.difficulty === 'Easy' ? 'bg-green-500' :
                  trail.difficulty === 'Moderate' ? 'bg-yellow-500' : 'bg-red-500'
                }`}></span>
                <p className="text-sm font-medium text-gray-900">{trail.difficulty}</p>
                <p className="text-xs text-gray-500">Difficulty</p>
              </div>
            </div>

            {/* Description */}
            <p className="text-gray-700 text-sm mb-4">{trail.description}</p>

            {/* Features */}
            {trail.features && trail.features.length > 0 && (
              <div className="mb-4">
                <p className="text-sm font-medium text-gray-900 mb-2">Features:</p>
                <div className="flex flex-wrap gap-1">
                  {trail.features.map((feature, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Score Breakdown */}
            <div className="border-t border-gray-200 pt-4">
              <p className="text-sm font-medium text-gray-900 mb-2">Score Breakdown:</p>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600">Wasatch Front (40%):</span>
                  <span className="font-medium">{trail.scoreBreakdown?.wasatchFront || 0}/100</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600">Elevation Range (25%):</span>
                  <span className="font-medium">{trail.scoreBreakdown?.elevationRange || 0}/100</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600">Trail System (20%):</span>
                  <span className="font-medium">{trail.scoreBreakdown?.establishedTrail || 0}/100</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600">Accessibility (15%):</span>
                  <span className="font-medium">{trail.scoreBreakdown?.hikeTimeRange || 0}/100</span>
                </div>
              </div>
              
              {trail.clueBonus > 0 && (
                <div className="mt-2 p-2 bg-treasure-50 rounded">
                  <p className="text-xs text-treasure-800">
                    <strong>Clue Bonus:</strong> +{trail.clueBonus.toFixed(1)} points from {trail.matchedClues?.length || 0} matching clues
                  </p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* No Results */}
      {filteredBySearch.length === 0 && (
        <div className="text-center py-12">
          <Mountain className="w-16 h-16 mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No trails found</h3>
          <p className="text-gray-600">Try adjusting your search terms or filters</p>
        </div>
      )}
    </div>
  );
}

export default TrailDatabase;
