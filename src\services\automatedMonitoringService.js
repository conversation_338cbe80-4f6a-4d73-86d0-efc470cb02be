// Automated clue monitoring and integration service
import axios from 'axios';
import { EventEmitter } from 'events';

class AutomatedMonitoringService extends EventEmitter {
  constructor() {
    super();
    this.isMonitoring = false;
    this.monitoringInterval = null;
    this.checkInterval = 30 * 60 * 1000; // 30 minutes in milliseconds
    this.rateLimitDelay = 30000; // 30 seconds between requests
    this.maxRetries = 3;
    this.backoffMultiplier = 2;
    
    // Monitoring targets configuration
    this.monitoringTargets = [
      {
        id: 'instagram_cline',
        name: '<PERSON> Instagram',
        url: 'https://www.instagram.com/the.cline.fam/',
        type: 'social_media',
        priority: 'high',
        selectors: {
          posts: '.x1lliihq', // Instagram post selectors (may change)
          captions: '[data-testid="post-caption"]'
        },
        keywords: ['treasure', 'hunt', 'clue', 'hint', 'riddle', 'utah'],
        lastChecked: null,
        lastContent: null,
        enabled: true
      },
      {
        id: 'abc4_news',
        name: 'ABC4 Utah Treasure Hunt',
        url: 'https://www.abc4.com/news/utah-treasure-hunt/',
        type: 'news',
        priority: 'high',
        selectors: {
          articles: '.post-item',
          headlines: '.post-title',
          content: '.post-content'
        },
        keywords: ['treasure hunt', 'clue', 'hint', 'david cline', 'john maxim'],
        lastChecked: null,
        lastContent: null,
        enabled: true
      },
      {
        id: 'ksl_news',
        name: 'KSL News',
        url: 'https://www.ksl.com/search?q=utah+treasure+hunt',
        type: 'news',
        priority: 'medium',
        selectors: {
          articles: '.search-result',
          headlines: '.headline',
          content: '.summary'
        },
        keywords: ['utah treasure hunt', 'treasure found', 'clue released'],
        lastChecked: null,
        lastContent: null,
        enabled: true
      },
      {
        id: 'fox13_news',
        name: 'Fox13 Utah',
        url: 'https://www.fox13now.com/search?q=utah+treasure+hunt',
        type: 'news',
        priority: 'medium',
        selectors: {
          articles: '.article-item',
          headlines: '.article-title',
          content: '.article-summary'
        },
        keywords: ['utah treasure hunt', 'treasure hunting', 'clue'],
        lastChecked: null,
        lastContent: null,
        enabled: true
      },
      {
        id: 'deseret_news',
        name: 'Deseret News',
        url: 'https://www.deseret.com/search?q=utah+treasure+hunt',
        type: 'news',
        priority: 'medium',
        selectors: {
          articles: '.search-result-item',
          headlines: '.headline',
          content: '.excerpt'
        },
        keywords: ['utah treasure hunt', 'treasure', 'hunt'],
        lastChecked: null,
        lastContent: null,
        enabled: true
      }
    ];

    this.auditLog = [];
    this.pendingReview = [];
    this.detectedClues = [];
  }

  /**
   * Start automated monitoring
   */
  async startMonitoring() {
    if (this.isMonitoring) {
      console.log('Monitoring already active');
      return;
    }

    console.log('Starting automated clue monitoring...');
    this.isMonitoring = true;
    this.emit('monitoringStarted');

    // Initial check
    await this.performMonitoringCycle();

    // Set up interval for continuous monitoring
    this.monitoringInterval = setInterval(async () => {
      await this.performMonitoringCycle();
    }, this.checkInterval);

    this.logAuditEvent('monitoring_started', 'Automated monitoring service started');
  }

  /**
   * Stop automated monitoring
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      console.log('Monitoring not active');
      return;
    }

    console.log('Stopping automated clue monitoring...');
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    this.emit('monitoringStopped');
    this.logAuditEvent('monitoring_stopped', 'Automated monitoring service stopped');
  }

  /**
   * Perform a complete monitoring cycle
   */
  async performMonitoringCycle() {
    console.log('Starting monitoring cycle...');
    const cycleStart = new Date();
    let newCluesFound = 0;

    for (const target of this.monitoringTargets) {
      if (!target.enabled) continue;

      try {
        await this.delay(this.rateLimitDelay);
        const newContent = await this.checkTarget(target);
        
        if (newContent && newContent.length > 0) {
          newCluesFound += newContent.length;
          await this.processNewContent(target, newContent);
        }
      } catch (error) {
        console.error(`Error checking target ${target.name}:`, error);
        this.logAuditEvent('monitoring_error', `Error checking ${target.name}: ${error.message}`);
      }
    }

    const cycleEnd = new Date();
    const duration = cycleEnd - cycleStart;

    this.emit('monitoringCycleComplete', {
      duration,
      targetsChecked: this.monitoringTargets.filter(t => t.enabled).length,
      newCluesFound,
      timestamp: cycleEnd
    });

    this.logAuditEvent('monitoring_cycle', `Cycle completed: ${newCluesFound} new clues found in ${duration}ms`);
  }

  /**
   * Check a specific monitoring target for new content
   */
  async checkTarget(target, retryCount = 0) {
    try {
      console.log(`Checking ${target.name}...`);
      
      // Make request with proper headers and rate limiting
      const response = await axios.get(target.url, {
        headers: {
          'User-Agent': 'Utah Treasure Finder Monitor Bot 1.0',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1'
        },
        timeout: 30000 // 30 second timeout
      });

      // Extract content based on target type
      const extractedContent = await this.extractContent(target, response.data);
      
      // Check for new content since last check
      const newContent = this.identifyNewContent(target, extractedContent);
      
      // Update target's last checked info
      target.lastChecked = new Date();
      target.lastContent = extractedContent;

      return newContent;

    } catch (error) {
      if (retryCount < this.maxRetries) {
        const delay = this.rateLimitDelay * Math.pow(this.backoffMultiplier, retryCount);
        console.log(`Retrying ${target.name} in ${delay}ms (attempt ${retryCount + 1})`);
        await this.delay(delay);
        return this.checkTarget(target, retryCount + 1);
      }
      throw error;
    }
  }

  /**
   * Extract relevant content from HTML response
   */
  async extractContent(target, html) {
    // In a real implementation, this would use a proper HTML parser like Cheerio
    // For now, we'll simulate content extraction
    const extractedContent = [];

    // Simulate finding treasure hunt related content
    if (target.type === 'social_media') {
      // Simulate Instagram post detection
      const mockPosts = this.simulateInstagramContent(target);
      extractedContent.push(...mockPosts);
    } else if (target.type === 'news') {
      // Simulate news article detection
      const mockArticles = this.simulateNewsContent(target);
      extractedContent.push(...mockArticles);
    }

    return extractedContent;
  }

  /**
   * Simulate Instagram content for testing
   */
  simulateInstagramContent(target) {
    const mockContent = [];
    
    // Simulate occasional new posts during active hunt periods
    const currentMonth = new Date().getMonth() + 1;
    if (currentMonth >= 6 && currentMonth <= 8) { // June-August
      if (Math.random() < 0.1) { // 10% chance of new content
        mockContent.push({
          id: `ig_${Date.now()}`,
          type: 'instagram_post',
          content: 'New hint for the 2025 Utah Treasure Hunt! Remember, the treasure is within a 90-minute hike from parking.',
          timestamp: new Date().toISOString(),
          url: `${target.url}p/${Date.now()}`,
          confidence: 0.9,
          keywords: ['hint', 'treasure', '90-minute', 'hike']
        });
      }
    }

    return mockContent;
  }

  /**
   * Simulate news content for testing
   */
  simulateNewsContent(target) {
    const mockContent = [];
    
    // Simulate occasional news articles
    if (Math.random() < 0.05) { // 5% chance of new content
      mockContent.push({
        id: `news_${Date.now()}`,
        type: 'news_article',
        content: 'Utah treasure hunt organizers release new clue as hunt continues into its second month.',
        timestamp: new Date().toISOString(),
        url: `${target.url}article/${Date.now()}`,
        confidence: 0.8,
        keywords: ['treasure hunt', 'clue', 'organizers']
      });
    }

    return mockContent;
  }

  /**
   * Identify new content since last check
   */
  identifyNewContent(target, extractedContent) {
    if (!target.lastContent) {
      return extractedContent; // First check, all content is new
    }

    // Compare with previous content to find new items
    const lastContentIds = target.lastContent.map(item => item.id);
    return extractedContent.filter(item => !lastContentIds.includes(item.id));
  }

  /**
   * Process newly detected content
   */
  async processNewContent(target, newContent) {
    for (const content of newContent) {
      try {
        // Analyze content for treasure hunt relevance
        const analysis = await this.analyzeContent(content, target);
        
        if (analysis.isTreasureHuntRelated) {
          // Add to detected clues
          const clue = {
            id: content.id,
            source: target.name,
            sourceId: target.id,
            type: this.determineClueType(content.content),
            content: content.content,
            keywords: analysis.keywords,
            confidence: analysis.confidence,
            timestamp: content.timestamp,
            url: content.url,
            autoDetected: true,
            verified: analysis.confidence > 0.8,
            needsReview: analysis.confidence < 0.6
          };

          this.detectedClues.push(clue);

          if (clue.verified) {
            // Automatically integrate high-confidence clues
            await this.integrateClue(clue);
            this.emit('clueDetected', clue);
          } else {
            // Add to review queue for manual verification
            this.pendingReview.push(clue);
            this.emit('clueNeedsReview', clue);
          }

          this.logAuditEvent('clue_detected', `New clue detected from ${target.name}: ${content.content.substring(0, 100)}...`);
        }
      } catch (error) {
        console.error('Error processing content:', error);
        this.logAuditEvent('processing_error', `Error processing content: ${error.message}`);
      }
    }
  }

  /**
   * Analyze content for treasure hunt relevance
   */
  async analyzeContent(content, target) {
    const analysis = {
      isTreasureHuntRelated: false,
      confidence: 0,
      keywords: [],
      clueType: null
    };

    const text = content.content.toLowerCase();
    
    // Check for treasure hunt keywords
    const treasureKeywords = [
      'treasure', 'hunt', 'clue', 'hint', 'riddle', 'poem',
      'david cline', 'john maxim', 'utah treasure hunt',
      'chest', 'hidden', 'found', 'search', 'hike'
    ];

    const foundKeywords = treasureKeywords.filter(keyword => text.includes(keyword));
    analysis.keywords = foundKeywords;

    // Calculate confidence based on keyword matches and source reliability
    let confidence = 0;
    
    // Keyword scoring
    confidence += foundKeywords.length * 0.1;
    
    // Source reliability scoring
    if (target.priority === 'high') confidence += 0.3;
    else if (target.priority === 'medium') confidence += 0.2;
    else confidence += 0.1;

    // Content type scoring
    if (text.includes('clue') || text.includes('hint')) confidence += 0.2;
    if (text.includes('riddle') || text.includes('poem')) confidence += 0.3;
    if (text.includes('treasure found') || text.includes('winner')) confidence += 0.4;

    analysis.confidence = Math.min(confidence, 1.0);
    analysis.isTreasureHuntRelated = analysis.confidence > 0.3;

    return analysis;
  }

  /**
   * Determine clue type from content
   */
  determineClueType(content) {
    const text = content.toLowerCase();
    
    if (text.includes('riddle') || text.includes('poem')) return 'riddle';
    if (text.includes('hint') || text.includes('clue')) return 'hint';
    if (text.includes('found') || text.includes('winner')) return 'solution';
    if (text.includes('interview') || text.includes('explains')) return 'interview';
    
    return 'hint'; // Default type
  }

  /**
   * Integrate verified clue into the system
   */
  async integrateClue(clue) {
    try {
      // Send to backend for integration
      const response = await axios.post('/api/clues/auto-integrate', {
        clue: clue,
        autoDetected: true,
        timestamp: new Date().toISOString()
      });

      if (response.data.success) {
        this.emit('clueIntegrated', clue);
        this.logAuditEvent('clue_integrated', `Clue automatically integrated: ${clue.id}`);
      }
    } catch (error) {
      console.error('Error integrating clue:', error);
      this.logAuditEvent('integration_error', `Failed to integrate clue ${clue.id}: ${error.message}`);
    }
  }

  /**
   * Get monitoring status
   */
  getMonitoringStatus() {
    return {
      isActive: this.isMonitoring,
      targets: this.monitoringTargets.map(target => ({
        id: target.id,
        name: target.name,
        enabled: target.enabled,
        lastChecked: target.lastChecked,
        priority: target.priority
      })),
      detectedClues: this.detectedClues.length,
      pendingReview: this.pendingReview.length,
      checkInterval: this.checkInterval,
      lastCycle: this.auditLog.filter(log => log.type === 'monitoring_cycle').slice(-1)[0]
    };
  }

  /**
   * Update monitoring configuration
   */
  updateConfiguration(config) {
    if (config.checkInterval) {
      this.checkInterval = config.checkInterval;
      
      // Restart monitoring with new interval if currently active
      if (this.isMonitoring) {
        this.stopMonitoring();
        this.startMonitoring();
      }
    }

    if (config.targets) {
      config.targets.forEach(targetUpdate => {
        const target = this.monitoringTargets.find(t => t.id === targetUpdate.id);
        if (target) {
          Object.assign(target, targetUpdate);
        }
      });
    }

    this.logAuditEvent('config_updated', 'Monitoring configuration updated');
  }

  /**
   * Get pending review items
   */
  getPendingReview() {
    return this.pendingReview;
  }

  /**
   * Approve pending clue for integration
   */
  async approvePendingClue(clueId) {
    const clueIndex = this.pendingReview.findIndex(clue => clue.id === clueId);
    if (clueIndex === -1) return false;

    const clue = this.pendingReview[clueIndex];
    clue.verified = true;
    clue.manuallyApproved = true;

    await this.integrateClue(clue);
    this.pendingReview.splice(clueIndex, 1);
    
    this.logAuditEvent('clue_approved', `Clue manually approved and integrated: ${clueId}`);
    return true;
  }

  /**
   * Reject pending clue
   */
  rejectPendingClue(clueId, reason = '') {
    const clueIndex = this.pendingReview.findIndex(clue => clue.id === clueId);
    if (clueIndex === -1) return false;

    const clue = this.pendingReview[clueIndex];
    this.pendingReview.splice(clueIndex, 1);
    
    this.logAuditEvent('clue_rejected', `Clue rejected: ${clueId}. Reason: ${reason}`);
    return true;
  }

  /**
   * Get audit log
   */
  getAuditLog(limit = 100) {
    return this.auditLog.slice(-limit);
  }

  /**
   * Log audit event
   */
  logAuditEvent(type, message, data = {}) {
    const logEntry = {
      id: Date.now(),
      type,
      message,
      data,
      timestamp: new Date().toISOString()
    };

    this.auditLog.push(logEntry);
    
    // Keep only last 1000 entries
    if (this.auditLog.length > 1000) {
      this.auditLog = this.auditLog.slice(-1000);
    }
  }

  /**
   * Utility delay function
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default new AutomatedMonitoringService();
