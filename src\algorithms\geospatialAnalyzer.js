// Advanced Geospatial Analysis using Gaussian Mixture Models and Kernel Density Estimation
// Optimized for treasure location prediction

class GeospatialAnalyzer {
  constructor() {
    this.historicalLocations = [];
    this.gmmModel = null;
    this.kdeModel = null;
    this.clusterCenters = [];
    this.probabilityGrid = null;
  }

  /**
   * Add historical treasure locations for training
   * @param {Array} locations - Array of {lat, lon, year, metadata} objects
   */
  addHistoricalLocations(locations) {
    this.historicalLocations = locations.map(loc => ({
      coordinates: [loc.lat, loc.lon],
      year: loc.year,
      metadata: loc.metadata || {}
    }));
  }

  /**
   * Fit Gaussian Mixture Model to historical locations
   * @param {number} numComponents - Number of Gaussian components
   * @param {number} maxIterations - Maximum EM iterations
   * @returns {Object} - Fitted GMM model
   */
  fitGaussianMixtureModel(numComponents = 2, maxIterations = 100) {
    if (this.historicalLocations.length < numComponents) {
      throw new Error('Insufficient historical data for GMM fitting');
    }

    const data = this.historicalLocations.map(loc => loc.coordinates);
    
    // Initialize GMM parameters
    let mixingCoefficients = new Array(numComponents).fill(1 / numComponents);
    let means = this.initializeMeans(data, numComponents);
    let covariances = this.initializeCovariances(numComponents);
    
    let logLikelihood = -Infinity;
    let converged = false;
    
    for (let iter = 0; iter < maxIterations && !converged; iter++) {
      // E-step: Calculate responsibilities
      const responsibilities = this.calculateResponsibilities(data, mixingCoefficients, means, covariances);
      
      // M-step: Update parameters
      const newParams = this.updateParameters(data, responsibilities, numComponents);
      mixingCoefficients = newParams.mixingCoefficients;
      means = newParams.means;
      covariances = newParams.covariances;
      
      // Check convergence
      const newLogLikelihood = this.calculateLogLikelihood(data, mixingCoefficients, means, covariances);
      converged = Math.abs(newLogLikelihood - logLikelihood) < 1e-6;
      logLikelihood = newLogLikelihood;
    }

    this.gmmModel = {
      numComponents,
      mixingCoefficients,
      means,
      covariances,
      logLikelihood,
      converged
    };

    return this.gmmModel;
  }

  /**
   * Initialize means using k-means++ algorithm
   * @param {Array} data - Data points
   * @param {number} k - Number of clusters
   * @returns {Array} - Initial means
   */
  initializeMeans(data, k) {
    const means = [];
    
    // Choose first center randomly
    means.push([...data[Math.floor(Math.random() * data.length)]]);
    
    // Choose remaining centers using k-means++
    for (let i = 1; i < k; i++) {
      const distances = data.map(point => {
        const minDist = Math.min(...means.map(mean => this.euclideanDistance(point, mean)));
        return minDist * minDist;
      });
      
      const totalDist = distances.reduce((sum, d) => sum + d, 0);
      const threshold = Math.random() * totalDist;
      
      let cumSum = 0;
      for (let j = 0; j < data.length; j++) {
        cumSum += distances[j];
        if (cumSum >= threshold) {
          means.push([...data[j]]);
          break;
        }
      }
    }
    
    return means;
  }

  /**
   * Initialize covariance matrices
   * @param {number} numComponents - Number of components
   * @returns {Array} - Initial covariance matrices
   */
  initializeCovariances(numComponents) {
    return new Array(numComponents).fill(null).map(() => [
      [0.01, 0],    // Small initial variance
      [0, 0.01]
    ]);
  }

  /**
   * Calculate responsibilities (E-step)
   * @param {Array} data - Data points
   * @param {Array} mixingCoefficients - Mixing coefficients
   * @param {Array} means - Component means
   * @param {Array} covariances - Component covariances
   * @returns {Array} - Responsibility matrix
   */
  calculateResponsibilities(data, mixingCoefficients, means, covariances) {
    const responsibilities = [];
    
    for (let i = 0; i < data.length; i++) {
      const point = data[i];
      const pointResponsibilities = [];
      let totalProb = 0;
      
      // Calculate probability for each component
      for (let k = 0; k < mixingCoefficients.length; k++) {
        const prob = mixingCoefficients[k] * this.multivariateGaussianPDF(point, means[k], covariances[k]);
        pointResponsibilities.push(prob);
        totalProb += prob;
      }
      
      // Normalize responsibilities
      const normalizedResponsibilities = pointResponsibilities.map(r => r / (totalProb || 1e-10));
      responsibilities.push(normalizedResponsibilities);
    }
    
    return responsibilities;
  }

  /**
   * Update GMM parameters (M-step)
   * @param {Array} data - Data points
   * @param {Array} responsibilities - Responsibility matrix
   * @param {number} numComponents - Number of components
   * @returns {Object} - Updated parameters
   */
  updateParameters(data, responsibilities, numComponents) {
    const newMixingCoefficients = [];
    const newMeans = [];
    const newCovariances = [];
    
    for (let k = 0; k < numComponents; k++) {
      // Update mixing coefficient
      const Nk = responsibilities.reduce((sum, r) => sum + r[k], 0);
      newMixingCoefficients.push(Nk / data.length);
      
      // Update mean
      const weightedSum = [0, 0];
      for (let i = 0; i < data.length; i++) {
        weightedSum[0] += responsibilities[i][k] * data[i][0];
        weightedSum[1] += responsibilities[i][k] * data[i][1];
      }
      newMeans.push([weightedSum[0] / Nk, weightedSum[1] / Nk]);
      
      // Update covariance
      const covariance = [[0, 0], [0, 0]];
      for (let i = 0; i < data.length; i++) {
        const diff = [data[i][0] - newMeans[k][0], data[i][1] - newMeans[k][1]];
        covariance[0][0] += responsibilities[i][k] * diff[0] * diff[0];
        covariance[0][1] += responsibilities[i][k] * diff[0] * diff[1];
        covariance[1][0] += responsibilities[i][k] * diff[1] * diff[0];
        covariance[1][1] += responsibilities[i][k] * diff[1] * diff[1];
      }
      
      // Normalize and add regularization
      const regularization = 1e-6;
      covariance[0][0] = covariance[0][0] / Nk + regularization;
      covariance[0][1] = covariance[0][1] / Nk;
      covariance[1][0] = covariance[1][0] / Nk;
      covariance[1][1] = covariance[1][1] / Nk + regularization;
      
      newCovariances.push(covariance);
    }
    
    return {
      mixingCoefficients: newMixingCoefficients,
      means: newMeans,
      covariances: newCovariances
    };
  }

  /**
   * Calculate multivariate Gaussian PDF
   * @param {Array} x - Point coordinates
   * @param {Array} mean - Mean vector
   * @param {Array} covariance - Covariance matrix
   * @returns {number} - Probability density
   */
  multivariateGaussianPDF(x, mean, covariance) {
    const diff = [x[0] - mean[0], x[1] - mean[1]];
    const det = covariance[0][0] * covariance[1][1] - covariance[0][1] * covariance[1][0];
    
    if (det <= 0) return 1e-10; // Avoid singular matrices
    
    // Inverse of 2x2 matrix
    const invCov = [
      [covariance[1][1] / det, -covariance[0][1] / det],
      [-covariance[1][0] / det, covariance[0][0] / det]
    ];
    
    // Mahalanobis distance
    const mahalanobis = diff[0] * (invCov[0][0] * diff[0] + invCov[0][1] * diff[1]) +
                       diff[1] * (invCov[1][0] * diff[0] + invCov[1][1] * diff[1]);
    
    const normalization = 1 / (2 * Math.PI * Math.sqrt(det));
    return normalization * Math.exp(-0.5 * mahalanobis);
  }

  /**
   * Calculate log-likelihood of data given model
   * @param {Array} data - Data points
   * @param {Array} mixingCoefficients - Mixing coefficients
   * @param {Array} means - Component means
   * @param {Array} covariances - Component covariances
   * @returns {number} - Log-likelihood
   */
  calculateLogLikelihood(data, mixingCoefficients, means, covariances) {
    let logLikelihood = 0;
    
    for (const point of data) {
      let pointLikelihood = 0;
      for (let k = 0; k < mixingCoefficients.length; k++) {
        pointLikelihood += mixingCoefficients[k] * this.multivariateGaussianPDF(point, means[k], covariances[k]);
      }
      logLikelihood += Math.log(pointLikelihood || 1e-10);
    }
    
    return logLikelihood;
  }

  /**
   * Fit Kernel Density Estimation model
   * @param {number} bandwidth - KDE bandwidth parameter
   * @returns {Object} - KDE model
   */
  fitKernelDensityEstimation(bandwidth = null) {
    if (this.historicalLocations.length === 0) {
      throw new Error('No historical locations available for KDE');
    }

    const data = this.historicalLocations.map(loc => loc.coordinates);
    
    // Automatic bandwidth selection using Scott's rule
    if (bandwidth === null) {
      const n = data.length;
      const stdLat = this.calculateStandardDeviation(data.map(d => d[0]));
      const stdLon = this.calculateStandardDeviation(data.map(d => d[1]));
      bandwidth = Math.pow(n, -1/6) * Math.min(stdLat, stdLon);
    }

    this.kdeModel = {
      data: data,
      bandwidth: bandwidth,
      kernelType: 'gaussian'
    };

    return this.kdeModel;
  }

  /**
   * Calculate probability density at a point using KDE
   * @param {Array} point - [lat, lon] coordinates
   * @returns {number} - Probability density
   */
  calculateKDEProbability(point) {
    if (!this.kdeModel) {
      throw new Error('KDE model not fitted');
    }

    const { data, bandwidth } = this.kdeModel;
    let density = 0;

    for (const dataPoint of data) {
      const distance = this.euclideanDistance(point, dataPoint);
      density += this.gaussianKernel(distance / bandwidth);
    }

    return density / (data.length * bandwidth * bandwidth);
  }

  /**
   * Gaussian kernel function
   * @param {number} u - Normalized distance
   * @returns {number} - Kernel value
   */
  gaussianKernel(u) {
    return (1 / Math.sqrt(2 * Math.PI)) * Math.exp(-0.5 * u * u);
  }

  /**
   * Calculate probability for a trail location
   * @param {Array} coordinates - [lat, lon] of trail
   * @returns {Object} - Probability analysis
   */
  calculateLocationProbability(coordinates) {
    const results = {
      coordinates: coordinates,
      gmmProbability: 0,
      kdeProbability: 0,
      proximityScore: 0,
      clusterAssignment: null,
      analysis: {}
    };

    // GMM probability
    if (this.gmmModel) {
      results.gmmProbability = this.calculateGMMProbability(coordinates);
      results.clusterAssignment = this.assignToCluster(coordinates);
    }

    // KDE probability
    if (this.kdeModel) {
      results.kdeProbability = this.calculateKDEProbability(coordinates);
    }

    // Proximity to historical locations
    results.proximityScore = this.calculateProximityScore(coordinates);

    // Distance analysis
    results.analysis = this.analyzeLocationDistances(coordinates);

    return results;
  }

  /**
   * Calculate GMM probability for a point
   * @param {Array} point - [lat, lon] coordinates
   * @returns {number} - GMM probability
   */
  calculateGMMProbability(point) {
    if (!this.gmmModel) return 0;

    const { mixingCoefficients, means, covariances } = this.gmmModel;
    let probability = 0;

    for (let k = 0; k < mixingCoefficients.length; k++) {
      probability += mixingCoefficients[k] * this.multivariateGaussianPDF(point, means[k], covariances[k]);
    }

    return probability;
  }

  /**
   * Assign point to most likely cluster
   * @param {Array} point - [lat, lon] coordinates
   * @returns {Object} - Cluster assignment
   */
  assignToCluster(point) {
    if (!this.gmmModel) return null;

    const { mixingCoefficients, means, covariances } = this.gmmModel;
    let maxProbability = 0;
    let assignedCluster = 0;
    const clusterProbabilities = [];

    for (let k = 0; k < mixingCoefficients.length; k++) {
      const prob = mixingCoefficients[k] * this.multivariateGaussianPDF(point, means[k], covariances[k]);
      clusterProbabilities.push(prob);
      
      if (prob > maxProbability) {
        maxProbability = prob;
        assignedCluster = k;
      }
    }

    const totalProb = clusterProbabilities.reduce((sum, p) => sum + p, 0);
    const normalizedProbs = clusterProbabilities.map(p => p / (totalProb || 1));

    return {
      clusterId: assignedCluster,
      probability: maxProbability,
      allProbabilities: normalizedProbs,
      clusterCenter: means[assignedCluster]
    };
  }

  /**
   * Calculate proximity score to historical locations
   * @param {Array} point - [lat, lon] coordinates
   * @returns {number} - Proximity score (0-1)
   */
  calculateProximityScore(point) {
    if (this.historicalLocations.length === 0) return 0;

    const distances = this.historicalLocations.map(loc => 
      this.haversineDistance(point, loc.coordinates)
    );

    const minDistance = Math.min(...distances);
    const avgDistance = distances.reduce((sum, d) => sum + d, 0) / distances.length;

    // Exponential decay function
    const sigma = 5; // 5 km standard deviation
    const proximityScore = Math.exp(-minDistance / sigma);

    return {
      score: proximityScore,
      minDistance: minDistance,
      avgDistance: avgDistance,
      closestLocation: this.historicalLocations[distances.indexOf(minDistance)]
    };
  }

  /**
   * Analyze distances to all historical locations
   * @param {Array} point - [lat, lon] coordinates
   * @returns {Object} - Distance analysis
   */
  analyzeLocationDistances(point) {
    const distances = this.historicalLocations.map(loc => ({
      year: loc.year,
      distance: this.haversineDistance(point, loc.coordinates),
      coordinates: loc.coordinates
    }));

    distances.sort((a, b) => a.distance - b.distance);

    return {
      closest: distances[0],
      farthest: distances[distances.length - 1],
      average: distances.reduce((sum, d) => sum + d.distance, 0) / distances.length,
      median: distances[Math.floor(distances.length / 2)].distance,
      allDistances: distances
    };
  }

  /**
   * Calculate Haversine distance between two points
   * @param {Array} point1 - [lat, lon] coordinates
   * @param {Array} point2 - [lat, lon] coordinates
   * @returns {number} - Distance in kilometers
   */
  haversineDistance(point1, point2) {
    const R = 6371; // Earth's radius in kilometers
    const lat1Rad = point1[0] * Math.PI / 180;
    const lat2Rad = point2[0] * Math.PI / 180;
    const deltaLatRad = (point2[0] - point1[0]) * Math.PI / 180;
    const deltaLonRad = (point2[1] - point1[1]) * Math.PI / 180;

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Calculate Euclidean distance between two points
   * @param {Array} point1 - Coordinates
   * @param {Array} point2 - Coordinates
   * @returns {number} - Euclidean distance
   */
  euclideanDistance(point1, point2) {
    const dx = point1[0] - point2[0];
    const dy = point1[1] - point2[1];
    return Math.sqrt(dx * dx + dy * dy);
  }

  /**
   * Calculate standard deviation
   * @param {Array} values - Array of numbers
   * @returns {number} - Standard deviation
   */
  calculateStandardDeviation(values) {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }

  /**
   * Generate probability heatmap for a region
   * @param {Object} bounds - {north, south, east, west} boundaries
   * @param {number} resolution - Grid resolution
   * @returns {Array} - 2D probability grid
   */
  generateProbabilityHeatmap(bounds, resolution = 50) {
    const latStep = (bounds.north - bounds.south) / resolution;
    const lonStep = (bounds.east - bounds.west) / resolution;
    const grid = [];

    for (let i = 0; i <= resolution; i++) {
      const row = [];
      const lat = bounds.south + i * latStep;
      
      for (let j = 0; j <= resolution; j++) {
        const lon = bounds.west + j * lonStep;
        const point = [lat, lon];
        
        let probability = 0;
        if (this.gmmModel) {
          probability = this.calculateGMMProbability(point);
        } else if (this.kdeModel) {
          probability = this.calculateKDEProbability(point);
        }
        
        row.push({
          lat: lat,
          lon: lon,
          probability: probability
        });
      }
      grid.push(row);
    }

    this.probabilityGrid = grid;
    return grid;
  }

  /**
   * Export geospatial model
   * @returns {Object} - Serializable model data
   */
  exportModel() {
    return {
      historicalLocations: this.historicalLocations,
      gmmModel: this.gmmModel,
      kdeModel: this.kdeModel,
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * Import geospatial model
   * @param {Object} modelData - Previously exported model
   */
  importModel(modelData) {
    this.historicalLocations = modelData.historicalLocations || [];
    this.gmmModel = modelData.gmmModel;
    this.kdeModel = modelData.kdeModel;
  }
}

export default GeospatialAnalyzer;
