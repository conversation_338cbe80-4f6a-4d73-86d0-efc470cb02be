// Advanced NLP Analyzer using TF-IDF and Semantic Analysis
// Optimized for treasure hunt content analysis

class NLPAnalyzer {
  constructor() {
    this.vocabulary = new Map();
    this.documentFrequency = new Map();
    this.totalDocuments = 0;
    this.treasureHuntTerms = this.initializeTreasureHuntTerms();
    this.stopWords = this.initializeStopWords();
  }

  /**
   * Initialize treasure hunt specific terminology
   */
  initializeTreasureHuntTerms() {
    return {
      locations: ['mountain', 'peak', 'canyon', 'trail', 'hike', 'elevation', 'ridge', 'valley', 'overlook', 'vista'],
      directions: ['north', 'south', 'east', 'west', 'up', 'down', 'left', 'right', 'above', 'below'],
      features: ['water', 'river', 'lake', 'stream', 'falls', 'spring', 'rock', 'stone', 'cliff', 'tree', 'forest'],
      treasure: ['treasure', 'chest', 'gold', 'oro', 'hidden', 'buried', 'find', 'search', 'hunt', 'clue', 'hint'],
      cultural: ['goonies', 'church', 'cathedral', 'temple', 'historic', 'monument', 'pioneer'],
      utah: ['wasatch', 'salt lake', 'ogden', 'provo', 'bountiful', 'millcreek', 'cottonwood', 'park city', 'utah'],
      organizers: ['david cline', 'john maxim', 'organizer', 'creator'],
      accessibility: ['parking', 'trailhead', 'access', 'hike time', 'distance', 'difficulty', 'moderate', 'easy']
    };
  }

  /**
   * Initialize stop words for filtering
   */
  initializeStopWords() {
    return new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
    ]);
  }

  /**
   * Preprocess text for analysis
   * @param {string} text - Input text
   * @returns {Array} - Processed tokens
   */
  preprocessText(text) {
    if (!text) return [];

    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Remove punctuation
      .split(/\s+/)
      .filter(token => token.length > 2 && !this.stopWords.has(token))
      .map(token => this.stemWord(token));
  }

  /**
   * Simple stemming algorithm
   * @param {string} word - Word to stem
   * @returns {string} - Stemmed word
   */
  stemWord(word) {
    // Simple suffix removal rules
    const suffixes = ['ing', 'ed', 'er', 'est', 'ly', 's'];
    
    for (const suffix of suffixes) {
      if (word.endsWith(suffix) && word.length > suffix.length + 2) {
        return word.slice(0, -suffix.length);
      }
    }
    
    return word;
  }

  /**
   * Build TF-IDF model from document corpus
   * @param {Array} documents - Array of {id, text, type} objects
   */
  buildModel(documents) {
    this.totalDocuments = documents.length;
    this.vocabulary.clear();
    this.documentFrequency.clear();

    // First pass: build vocabulary and document frequency
    documents.forEach(doc => {
      const tokens = this.preprocessText(doc.text);
      const uniqueTokens = new Set(tokens);

      uniqueTokens.forEach(token => {
        this.documentFrequency.set(token, (this.documentFrequency.get(token) || 0) + 1);
      });
    });

    // Second pass: calculate TF-IDF vectors
    documents.forEach(doc => {
      const tokens = this.preprocessText(doc.text);
      const tfidfVector = this.calculateTFIDF(tokens);
      this.vocabulary.set(doc.id, {
        tokens: tokens,
        tfidfVector: tfidfVector,
        type: doc.type,
        text: doc.text
      });
    });
  }

  /**
   * Calculate TF-IDF vector for a document
   * @param {Array} tokens - Document tokens
   * @returns {Map} - TF-IDF vector
   */
  calculateTFIDF(tokens) {
    const termFrequency = new Map();
    const tfidfVector = new Map();

    // Calculate term frequency
    tokens.forEach(token => {
      termFrequency.set(token, (termFrequency.get(token) || 0) + 1);
    });

    // Calculate TF-IDF for each term
    termFrequency.forEach((tf, term) => {
      const normalizedTF = tf / tokens.length;
      const df = this.documentFrequency.get(term) || 1;
      const idf = Math.log(this.totalDocuments / df);
      tfidfVector.set(term, normalizedTF * idf);
    });

    return tfidfVector;
  }

  /**
   * Calculate cosine similarity between two TF-IDF vectors
   * @param {Map} vector1 - First TF-IDF vector
   * @param {Map} vector2 - Second TF-IDF vector
   * @returns {number} - Cosine similarity (0-1)
   */
  cosineSimilarity(vector1, vector2) {
    const allTerms = new Set([...vector1.keys(), ...vector2.keys()]);
    
    let dotProduct = 0;
    let magnitude1 = 0;
    let magnitude2 = 0;

    allTerms.forEach(term => {
      const val1 = vector1.get(term) || 0;
      const val2 = vector2.get(term) || 0;
      
      dotProduct += val1 * val2;
      magnitude1 += val1 * val1;
      magnitude2 += val2 * val2;
    });

    if (magnitude1 === 0 || magnitude2 === 0) return 0;
    
    return dotProduct / (Math.sqrt(magnitude1) * Math.sqrt(magnitude2));
  }

  /**
   * Analyze text for treasure hunt relevance
   * @param {string} text - Text to analyze
   * @returns {Object} - Analysis results
   */
  analyzeTreasureHuntRelevance(text) {
    const tokens = this.preprocessText(text);
    const tfidfVector = this.calculateTFIDF(tokens);
    
    // Calculate category scores
    const categoryScores = {};
    Object.keys(this.treasureHuntTerms).forEach(category => {
      categoryScores[category] = this.calculateCategoryScore(tokens, this.treasureHuntTerms[category]);
    });

    // Calculate overall relevance score
    const relevanceScore = this.calculateRelevanceScore(categoryScores, tokens.length);
    
    // Extract key phrases
    const keyPhrases = this.extractKeyPhrases(text);
    
    // Sentiment analysis
    const sentiment = this.analyzeSentiment(text);
    
    // Confidence estimation
    const confidence = this.calculateConfidence(categoryScores, tokens.length);

    return {
      relevanceScore: relevanceScore,
      confidence: confidence,
      categoryScores: categoryScores,
      keyPhrases: keyPhrases,
      sentiment: sentiment,
      tokenCount: tokens.length,
      uniqueTerms: new Set(tokens).size,
      treasureHuntTerms: this.findTreasureHuntTerms(tokens),
      metadata: {
        analyzedAt: new Date().toISOString(),
        textLength: text.length
      }
    };
  }

  /**
   * Calculate category-specific score
   * @param {Array} tokens - Document tokens
   * @param {Array} categoryTerms - Terms for specific category
   * @returns {number} - Category score (0-1)
   */
  calculateCategoryScore(tokens, categoryTerms) {
    const matches = tokens.filter(token => 
      categoryTerms.some(term => token.includes(term) || term.includes(token))
    );
    
    return tokens.length > 0 ? matches.length / tokens.length : 0;
  }

  /**
   * Calculate overall relevance score
   * @param {Object} categoryScores - Scores for each category
   * @param {number} tokenCount - Total token count
   * @returns {number} - Relevance score (0-1)
   */
  calculateRelevanceScore(categoryScores, tokenCount) {
    // Weighted combination of category scores
    const weights = {
      treasure: 0.25,
      locations: 0.20,
      utah: 0.15,
      features: 0.10,
      directions: 0.10,
      accessibility: 0.08,
      cultural: 0.07,
      organizers: 0.05
    };

    let weightedScore = 0;
    Object.keys(weights).forEach(category => {
      weightedScore += (categoryScores[category] || 0) * weights[category];
    });

    // Boost score for longer, more detailed texts
    const lengthBonus = Math.min(0.2, tokenCount / 100);
    
    return Math.min(1.0, weightedScore + lengthBonus);
  }

  /**
   * Extract key phrases from text
   * @param {string} text - Input text
   * @returns {Array} - Key phrases with scores
   */
  extractKeyPhrases(text) {
    const phrases = [];
    const words = text.toLowerCase().split(/\s+/);
    
    // Extract 2-3 word phrases
    for (let i = 0; i < words.length - 1; i++) {
      const bigram = words.slice(i, i + 2).join(' ');
      const trigram = i < words.length - 2 ? words.slice(i, i + 3).join(' ') : null;
      
      if (this.isPhraseRelevant(bigram)) {
        phrases.push({ phrase: bigram, type: 'bigram', score: this.scorePhraseRelevance(bigram) });
      }
      
      if (trigram && this.isPhraseRelevant(trigram)) {
        phrases.push({ phrase: trigram, type: 'trigram', score: this.scorePhraseRelevance(trigram) });
      }
    }

    return phrases
      .sort((a, b) => b.score - a.score)
      .slice(0, 10); // Top 10 phrases
  }

  /**
   * Check if phrase is relevant to treasure hunting
   * @param {string} phrase - Phrase to check
   * @returns {boolean} - Relevance flag
   */
  isPhraseRelevant(phrase) {
    const allTerms = Object.values(this.treasureHuntTerms).flat();
    return allTerms.some(term => phrase.includes(term));
  }

  /**
   * Score phrase relevance
   * @param {string} phrase - Phrase to score
   * @returns {number} - Relevance score
   */
  scorePhraseRelevance(phrase) {
    let score = 0;
    Object.entries(this.treasureHuntTerms).forEach(([category, terms]) => {
      const matches = terms.filter(term => phrase.includes(term));
      score += matches.length * (category === 'treasure' ? 2 : 1);
    });
    return score;
  }

  /**
   * Simple sentiment analysis
   * @param {string} text - Text to analyze
   * @returns {Object} - Sentiment scores
   */
  analyzeSentiment(text) {
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'beautiful', 'perfect', 'love', 'best', 'wonderful'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'worst', 'difficult', 'hard', 'impossible', 'dangerous'];
    const uncertaintyWords = ['maybe', 'perhaps', 'possibly', 'might', 'could', 'uncertain', 'unclear', 'confusing'];
    
    const words = text.toLowerCase().split(/\s+/);
    
    const positive = words.filter(word => positiveWords.includes(word)).length;
    const negative = words.filter(word => negativeWords.includes(word)).length;
    const uncertainty = words.filter(word => uncertaintyWords.includes(word)).length;
    
    const total = positive + negative + uncertainty;
    
    return {
      positive: total > 0 ? positive / total : 0,
      negative: total > 0 ? negative / total : 0,
      uncertainty: total > 0 ? uncertainty / total : 0,
      neutral: total > 0 ? 1 - (positive + negative + uncertainty) / total : 1
    };
  }

  /**
   * Find treasure hunt specific terms in tokens
   * @param {Array} tokens - Document tokens
   * @returns {Array} - Found treasure hunt terms
   */
  findTreasureHuntTerms(tokens) {
    const found = [];
    const allTerms = Object.values(this.treasureHuntTerms).flat();
    
    tokens.forEach(token => {
      allTerms.forEach(term => {
        if (token.includes(term) || term.includes(token)) {
          found.push({ token, matchedTerm: term });
        }
      });
    });
    
    return found;
  }

  /**
   * Calculate confidence in analysis
   * @param {Object} categoryScores - Category scores
   * @param {number} tokenCount - Token count
   * @returns {number} - Confidence score (0-1)
   */
  calculateConfidence(categoryScores, tokenCount) {
    // Confidence based on multiple factors
    const categoryCount = Object.values(categoryScores).filter(score => score > 0).length;
    const maxCategoryScore = Math.max(...Object.values(categoryScores));
    const textLength = tokenCount;
    
    // Normalize factors
    const categoryFactor = Math.min(1, categoryCount / 4); // 4+ categories = high confidence
    const scoreFactor = maxCategoryScore;
    const lengthFactor = Math.min(1, textLength / 20); // 20+ tokens = good length
    
    return (categoryFactor * 0.4 + scoreFactor * 0.4 + lengthFactor * 0.2);
  }

  /**
   * Find similar documents using cosine similarity
   * @param {string} queryText - Query text
   * @param {number} topK - Number of results to return
   * @returns {Array} - Similar documents with scores
   */
  findSimilarDocuments(queryText, topK = 5) {
    const queryTokens = this.preprocessText(queryText);
    const queryVector = this.calculateTFIDF(queryTokens);
    
    const similarities = [];
    
    this.vocabulary.forEach((doc, docId) => {
      const similarity = this.cosineSimilarity(queryVector, doc.tfidfVector);
      similarities.push({
        docId: docId,
        similarity: similarity,
        type: doc.type,
        text: doc.text.substring(0, 200) + '...'
      });
    });
    
    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK);
  }

  /**
   * Export model for persistence
   * @returns {Object} - Serializable model data
   */
  exportModel() {
    return {
      vocabulary: Array.from(this.vocabulary.entries()),
      documentFrequency: Array.from(this.documentFrequency.entries()),
      totalDocuments: this.totalDocuments,
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * Import previously exported model
   * @param {Object} modelData - Exported model data
   */
  importModel(modelData) {
    this.vocabulary = new Map(modelData.vocabulary);
    this.documentFrequency = new Map(modelData.documentFrequency);
    this.totalDocuments = modelData.totalDocuments;
  }
}

export default NLPAnalyzer;
