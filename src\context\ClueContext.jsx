import React, { createContext, useContext, useState, useEffect } from 'react';

const ClueContext = createContext();

export function useClues() {
  const context = useContext(ClueContext);
  if (!context) {
    throw new Error('useClues must be used within a ClueProvider');
  }
  return context;
}

export function ClueProvider({ children }) {
  const [clues, setClues] = useState([]);
  const [currentHunt, setCurrentHunt] = useState({
    year: 2024,
    status: 'active', // active, completed, upcoming
    riddle: '',
    hints: [],
    startDate: '2024-06-15',
    prizeValue: 25000
  });

  // Sample clues for demonstration
  useEffect(() => {
    const sampleClues = [
      {
        id: 1,
        type: 'riddle',
        content: 'Si sufres dolor que se cura con oro, Busca el atajo donde canta el coro',
        dateAdded: '2024-06-15',
        keywords: ['oro', 'gold', 'coro', 'choir', 'cathedral', 'church'],
        confidence: 8,
        analysis: 'Spanish riddle mentioning gold and choir - possible religious/cathedral connection'
      },
      {
        id: 2,
        type: 'hint',
        content: 'At an average pace, the treasure chest is between a 0-90 minute hike from where you leave the car. There is no need to venture too far.',
        dateAdded: '2024-07-01',
        keywords: ['90 minute', 'hike', 'car', 'parking'],
        confidence: 10,
        analysis: 'Confirms accessibility requirement - moderate hike time'
      },
      {
        id: 3,
        type: 'hint',
        content: 'The only pop culture reference in the poem is to The Goonies.',
        dateAdded: '2024-07-01',
        keywords: ['goonies', 'treasure', 'adventure'],
        confidence: 6,
        analysis: 'Pop culture reference to treasure hunting movie'
      }
    ];
    setClues(sampleClues);
  }, []);

  const addClue = (clueData) => {
    const newClue = {
      id: Date.now(),
      dateAdded: new Date().toISOString().split('T')[0],
      confidence: 5,
      analysis: '',
      keywords: [],
      ...clueData
    };
    setClues(prev => [...prev, newClue]);
    return newClue;
  };

  const updateClue = (id, updates) => {
    setClues(prev => prev.map(clue => 
      clue.id === id ? { ...clue, ...updates } : clue
    ));
  };

  const deleteClue = (id) => {
    setClues(prev => prev.filter(clue => clue.id !== id));
  };

  const analyzeClueText = (text) => {
    // Simple keyword extraction - could be enhanced with NLP
    const commonKeywords = [
      'mountain', 'peak', 'canyon', 'trail', 'hike', 'elevation',
      'north', 'south', 'east', 'west', 'up', 'down',
      'water', 'river', 'lake', 'stream', 'falls',
      'rock', 'stone', 'cliff', 'ridge', 'valley',
      'tree', 'forest', 'pine', 'oak', 'aspen',
      'view', 'overlook', 'vista', 'panorama',
      'church', 'cathedral', 'temple', 'historic',
      'gold', 'treasure', 'hidden', 'buried'
    ];

    const foundKeywords = commonKeywords.filter(keyword =>
      text.toLowerCase().includes(keyword)
    );

    return {
      keywords: foundKeywords,
      wordCount: text.split(' ').length,
      hasCoordinates: /\d+\.\d+/.test(text),
      hasDirections: /north|south|east|west|left|right/i.test(text),
      hasNumbers: /\d+/.test(text)
    };
  };

  const getCluesByType = (type) => {
    return clues.filter(clue => clue.type === type);
  };

  const getRecentClues = (days = 7) => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    
    return clues.filter(clue => {
      const clueDate = new Date(clue.dateAdded);
      return clueDate >= cutoffDate;
    });
  };

  const getAllKeywords = () => {
    const allKeywords = clues.flatMap(clue => clue.keywords || []);
    const keywordCounts = {};
    
    allKeywords.forEach(keyword => {
      keywordCounts[keyword] = (keywordCounts[keyword] || 0) + 1;
    });

    return Object.entries(keywordCounts)
      .sort(([,a], [,b]) => b - a)
      .map(([keyword, count]) => ({ keyword, count }));
  };

  const updateCurrentHunt = (updates) => {
    setCurrentHunt(prev => ({ ...prev, ...updates }));
  };

  const value = {
    clues,
    currentHunt,
    addClue,
    updateClue,
    deleteClue,
    analyzeClueText,
    getCluesByType,
    getRecentClues,
    getAllKeywords,
    updateCurrentHunt
  };

  return (
    <ClueContext.Provider value={value}>
      {children}
    </ClueContext.Provider>
  );
}
