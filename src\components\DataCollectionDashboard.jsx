import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, <PERSON>Chart, Pie, Cell } from 'recharts';
import { Database, RefreshCw, AlertCircle, CheckCircle, Clock, TrendingUp, Download, Play } from 'lucide-react';
import dataCollectionService from '../services/dataCollectionService';
import patternRecognitionEngine from '../services/patternRecognitionEngine';

function DataCollectionDashboard() {
  const [collectionStatus, setCollectionStatus] = useState(null);
  const [isCollecting, setIsCollecting] = useState(false);
  const [patternAnalysis, setPatternAnalysis] = useState(null);
  const [collectedData, setCollectedData] = useState({
    riddles: [],
    hints: [],
    solutions: [],
    interviews: [],
    communityTheories: [],
    newsArticles: []
  });

  useEffect(() => {
    loadCollectionStatus();
    loadCollectedData();
  }, []);

  const loadCollectionStatus = async () => {
    try {
      const status = await dataCollectionService.getCollectionStatus();
      setCollectionStatus(status);
    } catch (error) {
      console.error('Failed to load collection status:', error);
    }
  };

  const loadCollectedData = async () => {
    try {
      // In a real implementation, this would load from a database
      const mockData = {
        riddles: [
          { year: 2020, count: 1, language: 'english' },
          { year: 2021, count: 1, language: 'english' },
          { year: 2022, count: 1, language: 'english' },
          { year: 2023, count: 1, language: 'english' },
          { year: 2024, count: 1, language: 'spanish' }
        ],
        hints: [
          { year: 2020, count: 3 },
          { year: 2021, count: 5 },
          { year: 2022, count: 4 },
          { year: 2023, count: 6 },
          { year: 2024, count: 8 }
        ],
        solutions: [
          { year: 2020, found: true, location: 'Rocky Mouth Canyon' },
          { year: 2021, found: true, location: 'Heughs Canyon Trail' },
          { year: 2022, found: true, location: 'Ben Lomond Peak' },
          { year: 2023, found: true, location: 'Mueller Park Trail' },
          { year: 2024, found: false, location: 'Unknown' }
        ]
      };
      setCollectedData(mockData);
    } catch (error) {
      console.error('Failed to load collected data:', error);
    }
  };

  const triggerDataCollection = async () => {
    setIsCollecting(true);
    try {
      await dataCollectionService.triggerCollection('all');
      await loadCollectionStatus();
      await loadCollectedData();
    } catch (error) {
      console.error('Failed to trigger data collection:', error);
    } finally {
      setIsCollecting(false);
    }
  };

  const runPatternAnalysis = async () => {
    try {
      const analysis = await patternRecognitionEngine.analyzePatterns(collectedData);
      setPatternAnalysis(analysis);
    } catch (error) {
      console.error('Failed to run pattern analysis:', error);
    }
  };

  const exportData = () => {
    const exportData = {
      collectionStatus,
      collectedData,
      patternAnalysis,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'utah-treasure-data-export.json';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const COLORS = ['#eab308', '#3b82f6', '#10b981', '#f59e0b', '#ef4444'];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Data Collection Dashboard</h1>
          <p className="text-gray-600">Comprehensive treasure hunt data analysis and pattern recognition</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={runPatternAnalysis}
            className="btn-secondary flex items-center space-x-2"
          >
            <TrendingUp className="w-4 h-4" />
            <span>Analyze Patterns</span>
          </button>
          
          <button
            onClick={exportData}
            className="btn-secondary flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Export Data</span>
          </button>
          
          <button
            onClick={triggerDataCollection}
            disabled={isCollecting}
            className="btn-primary flex items-center space-x-2"
          >
            {isCollecting ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            <span>{isCollecting ? 'Collecting...' : 'Start Collection'}</span>
          </button>
        </div>
      </div>

      {/* Collection Status */}
      {collectionStatus && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="card">
            <div className="flex items-center space-x-3">
              <Database className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Sources</p>
                <p className="text-2xl font-bold text-gray-900">{collectionStatus.totalSources}</p>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="flex items-center space-x-3">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Successful</p>
                <p className="text-2xl font-bold text-gray-900">{collectionStatus.successfulSources}</p>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-8 h-8 text-red-600" />
              <div>
                <p className="text-sm text-gray-600">Failed</p>
                <p className="text-2xl font-bold text-gray-900">{collectionStatus.failedSources}</p>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="flex items-center space-x-3">
              <Clock className="w-8 h-8 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Data Quality</p>
                <p className="text-2xl font-bold text-gray-900">{collectionStatus.dataQuality}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Data Statistics */}
      {collectionStatus?.statistics && (
        <div className="card">
          <h2 className="text-xl font-bold text-gray-900 mb-6">Collected Data Statistics</h2>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {Object.entries(collectionStatus.statistics).map(([type, count]) => (
              <div key={type} className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-treasure-600">{count}</p>
                <p className="text-sm text-gray-600 capitalize">{type.replace(/([A-Z])/g, ' $1').trim()}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Data Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <h2 className="text-xl font-bold text-gray-900 mb-6">Hints Released by Year</h2>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={collectedData.hints}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" fill="#3b82f6" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        <div className="card">
          <h2 className="text-xl font-bold text-gray-900 mb-6">Data Source Distribution</h2>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={[
                  { name: 'News Articles', value: 18 },
                  { name: 'Social Media', value: 24 },
                  { name: 'Community Posts', value: 156 },
                  { name: 'Official Content', value: 38 }
                ]}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {[0, 1, 2, 3].map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Pattern Analysis Results */}
      {patternAnalysis && (
        <div className="card">
          <h2 className="text-xl font-bold text-gray-900 mb-6">Pattern Analysis Results</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Linguistic Patterns</h3>
              <div className="space-y-1 text-sm">
                <p>English: {patternAnalysis.linguisticPatterns?.languageUsage?.english || 0}%</p>
                <p>Spanish: {patternAnalysis.linguisticPatterns?.languageUsage?.spanish || 0}%</p>
                <p>Top Keywords: {Object.keys(patternAnalysis.linguisticPatterns?.keywordFrequency || {}).slice(0, 3).join(', ')}</p>
              </div>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <h3 className="font-semibold text-green-900 mb-2">Geographic Patterns</h3>
              <div className="space-y-1 text-sm">
                <p>Wasatch Front: {patternAnalysis.geographicPatterns?.locationClustering?.wasatchFront || 0}%</p>
                <p>Avg Distance: {patternAnalysis.geographicPatterns?.locationClustering?.averageDistance || 0} mi</p>
                <p>Elevation Range: {patternAnalysis.geographicPatterns?.elevationPreferences?.min || 0}-{patternAnalysis.geographicPatterns?.elevationPreferences?.max || 0}ft</p>
              </div>
            </div>
            
            <div className="p-4 bg-yellow-50 rounded-lg">
              <h3 className="font-semibold text-yellow-900 mb-2">Temporal Patterns</h3>
              <div className="space-y-1 text-sm">
                <p>Season: Summer preferred</p>
                <p>Duration: {patternAnalysis.temporalPatterns?.huntDuration?.average || 0} days avg</p>
                <p>Peak Months: {patternAnalysis.temporalPatterns?.seasonalTiming?.preferredMonths?.join(', ') || 'N/A'}</p>
              </div>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg">
              <h3 className="font-semibold text-purple-900 mb-2">Organizer Preferences</h3>
              <div className="space-y-1 text-sm">
                <p>Criteria: {patternAnalysis.organizerPreferences?.locationCriteria?.length || 0} identified</p>
                <p>Safety: {patternAnalysis.organizerPreferences?.safetyConsiderations?.length || 0} factors</p>
                <p>Avoidance: {patternAnalysis.organizerPreferences?.avoidanceFactors?.length || 0} factors</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Data Sources Status */}
      <div className="card">
        <h2 className="text-xl font-bold text-gray-900 mb-6">Data Sources Status</h2>
        
        <div className="space-y-4">
          {[
            { name: 'Instagram (@the.cline.fam)', status: 'active', lastUpdate: '2 hours ago', items: 24 },
            { name: 'ABC4 News Coverage', status: 'active', lastUpdate: '6 hours ago', items: 8 },
            { name: 'KSL News Articles', status: 'active', lastUpdate: '4 hours ago', items: 6 },
            { name: 'Reddit r/utahtreasurehunt', status: 'active', lastUpdate: '1 hour ago', items: 156 },
            { name: 'Deseret News Coverage', status: 'warning', lastUpdate: '2 days ago', items: 4 },
            { name: 'Fox13 News Coverage', status: 'error', lastUpdate: '1 week ago', items: 0 }
          ].map((source, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className={`w-3 h-3 rounded-full ${
                  source.status === 'active' ? 'bg-green-500' :
                  source.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                }`}></div>
                <div>
                  <h3 className="font-medium text-gray-900">{source.name}</h3>
                  <p className="text-sm text-gray-600">Last updated: {source.lastUpdate}</p>
                </div>
              </div>
              
              <div className="text-right">
                <p className="text-lg font-semibold text-gray-900">{source.items}</p>
                <p className="text-sm text-gray-600">items</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default DataCollectionDashboard;
