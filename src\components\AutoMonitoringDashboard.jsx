import React, { useState, useEffect } from 'react';
import { useClues } from '../context/ClueContext';
import automatedMonitoringService from '../services/automatedMonitoringService';
import { Play, Pause, Settings, Eye, Check, X, Clock, AlertCircle, CheckCircle, Activity } from 'lucide-react';

function AutoMonitoringDashboard() {
  const { addClue } = useClues();
  const [monitoringStatus, setMonitoringStatus] = useState(null);
  const [pendingReview, setPendingReview] = useState([]);
  const [auditLog, setAuditLog] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [showSettings, setShowSettings] = useState(false);
  const [settings, setSettings] = useState({
    checkInterval: 30, // minutes
    autoApproveHighConfidence: true,
    notificationsEnabled: true
  });

  useEffect(() => {
    // Set up event listeners
    automatedMonitoringService.on('monitoringStarted', handleMonitoringStarted);
    automatedMonitoringService.on('monitoringStopped', handleMonitoringStopped);
    automatedMonitoringService.on('clueDetected', handleClueDetected);
    automatedMonitoringService.on('clueNeedsReview', handleClueNeedsReview);
    automatedMonitoringService.on('clueIntegrated', handleClueIntegrated);
    automatedMonitoringService.on('monitoringCycleComplete', handleCycleComplete);

    // Load initial status
    loadMonitoringStatus();
    loadPendingReview();
    loadAuditLog();

    // Set up periodic status updates
    const statusInterval = setInterval(() => {
      loadMonitoringStatus();
      loadPendingReview();
      loadAuditLog();
    }, 60000); // Update every minute

    return () => {
      // Clean up event listeners
      automatedMonitoringService.removeAllListeners();
      clearInterval(statusInterval);
    };
  }, []);

  const loadMonitoringStatus = () => {
    const status = automatedMonitoringService.getMonitoringStatus();
    setMonitoringStatus(status);
  };

  const loadPendingReview = () => {
    const pending = automatedMonitoringService.getPendingReview();
    setPendingReview(pending);
  };

  const loadAuditLog = () => {
    const log = automatedMonitoringService.getAuditLog(50);
    setAuditLog(log);
  };

  const handleMonitoringStarted = () => {
    addNotification('Automated monitoring started', 'success');
    loadMonitoringStatus();
  };

  const handleMonitoringStopped = () => {
    addNotification('Automated monitoring stopped', 'info');
    loadMonitoringStatus();
  };

  const handleClueDetected = (clue) => {
    addNotification(`New clue detected from ${clue.source}`, 'success');
    loadMonitoringStatus();
  };

  const handleClueNeedsReview = (clue) => {
    addNotification(`New clue needs review from ${clue.source}`, 'warning');
    loadPendingReview();
  };

  const handleClueIntegrated = (clue) => {
    addNotification(`Clue automatically integrated: ${clue.type}`, 'success');
    // Add to main clue system
    addClue({
      type: clue.type,
      content: clue.content,
      keywords: clue.keywords,
      confidence: clue.confidence * 10, // Convert to 1-10 scale
      source: clue.source,
      autoDetected: true
    });
  };

  const handleCycleComplete = (cycleInfo) => {
    if (cycleInfo.newCluesFound > 0) {
      addNotification(`Monitoring cycle complete: ${cycleInfo.newCluesFound} new clues found`, 'info');
    }
    loadMonitoringStatus();
  };

  const addNotification = (message, type) => {
    const notification = {
      id: Date.now(),
      message,
      type,
      timestamp: new Date()
    };
    setNotifications(prev => [notification, ...prev.slice(0, 9)]); // Keep last 10
  };

  const startMonitoring = async () => {
    try {
      await automatedMonitoringService.startMonitoring();
    } catch (error) {
      addNotification(`Failed to start monitoring: ${error.message}`, 'error');
    }
  };

  const stopMonitoring = () => {
    automatedMonitoringService.stopMonitoring();
  };

  const approveClue = async (clueId) => {
    try {
      const success = await automatedMonitoringService.approvePendingClue(clueId);
      if (success) {
        addNotification('Clue approved and integrated', 'success');
        loadPendingReview();
      }
    } catch (error) {
      addNotification(`Failed to approve clue: ${error.message}`, 'error');
    }
  };

  const rejectClue = (clueId, reason = '') => {
    try {
      const success = automatedMonitoringService.rejectPendingClue(clueId, reason);
      if (success) {
        addNotification('Clue rejected', 'info');
        loadPendingReview();
      }
    } catch (error) {
      addNotification(`Failed to reject clue: ${error.message}`, 'error');
    }
  };

  const updateSettings = () => {
    automatedMonitoringService.updateConfiguration({
      checkInterval: settings.checkInterval * 60 * 1000 // Convert to milliseconds
    });
    addNotification('Settings updated', 'success');
    setShowSettings(false);
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  const getStatusColor = (isActive) => {
    return isActive ? 'text-green-600' : 'text-gray-600';
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'warning': return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      case 'error': return <X className="w-4 h-4 text-red-600" />;
      default: return <Activity className="w-4 h-4 text-blue-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Automated Clue Monitoring</h2>
          <p className="text-gray-600">Real-time monitoring of official treasure hunt sources</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="btn-secondary flex items-center space-x-2"
          >
            <Settings className="w-4 h-4" />
            <span>Settings</span>
          </button>
          
          {monitoringStatus?.isActive ? (
            <button
              onClick={stopMonitoring}
              className="btn-secondary flex items-center space-x-2 text-red-600 border-red-300 hover:bg-red-50"
            >
              <Pause className="w-4 h-4" />
              <span>Stop Monitoring</span>
            </button>
          ) : (
            <button
              onClick={startMonitoring}
              className="btn-primary flex items-center space-x-2"
            >
              <Play className="w-4 h-4" />
              <span>Start Monitoring</span>
            </button>
          )}
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="card bg-gray-50">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Monitoring Settings</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Check Interval (minutes)
              </label>
              <input
                type="number"
                min="5"
                max="120"
                value={settings.checkInterval}
                onChange={(e) => setSettings(prev => ({ ...prev, checkInterval: parseInt(e.target.value) }))}
                className="input-field"
              />
            </div>
            
            <div className="flex items-center">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={settings.autoApproveHighConfidence}
                  onChange={(e) => setSettings(prev => ({ ...prev, autoApproveHighConfidence: e.target.checked }))}
                  className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
                />
                <span className="text-sm text-gray-700">Auto-approve high confidence clues</span>
              </label>
            </div>
            
            <div className="flex items-center">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={settings.notificationsEnabled}
                  onChange={(e) => setSettings(prev => ({ ...prev, notificationsEnabled: e.target.checked }))}
                  className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
                />
                <span className="text-sm text-gray-700">Enable notifications</span>
              </label>
            </div>
          </div>
          
          <div className="mt-4 flex justify-end space-x-3">
            <button
              onClick={() => setShowSettings(false)}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>
            <button
              onClick={updateSettings}
              className="btn-primary"
            >
              Save Settings
            </button>
          </div>
        </div>
      )}

      {/* Status Overview */}
      {monitoringStatus && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="card">
            <div className="flex items-center space-x-3">
              <Activity className={`w-8 h-8 ${getStatusColor(monitoringStatus.isActive)}`} />
              <div>
                <p className="text-sm text-gray-600">Status</p>
                <p className="text-lg font-bold text-gray-900">
                  {monitoringStatus.isActive ? 'Active' : 'Inactive'}
                </p>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="flex items-center space-x-3">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Clues Detected</p>
                <p className="text-lg font-bold text-gray-900">{monitoringStatus.detectedClues}</p>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="flex items-center space-x-3">
              <Eye className="w-8 h-8 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Pending Review</p>
                <p className="text-lg font-bold text-gray-900">{monitoringStatus.pendingReview}</p>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="flex items-center space-x-3">
              <Clock className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Check Interval</p>
                <p className="text-lg font-bold text-gray-900">{Math.round(monitoringStatus.checkInterval / 60000)}m</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Notifications</h3>
          <div className="space-y-2">
            {notifications.slice(0, 5).map((notification) => (
              <div key={notification.id} className="flex items-center space-x-3 p-2 bg-gray-50 rounded">
                {getNotificationIcon(notification.type)}
                <span className="text-sm text-gray-700 flex-1">{notification.message}</span>
                <span className="text-xs text-gray-500">
                  {notification.timestamp.toLocaleTimeString()}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Monitoring Targets */}
      {monitoringStatus?.targets && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Monitoring Targets</h3>
          <div className="space-y-3">
            {monitoringStatus.targets.map((target) => (
              <div key={target.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${target.enabled ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                  <div>
                    <h4 className="font-medium text-gray-900">{target.name}</h4>
                    <p className="text-sm text-gray-600">
                      Priority: {target.priority} | Last checked: {target.lastChecked ? formatTimestamp(target.lastChecked) : 'Never'}
                    </p>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  target.priority === 'high' ? 'bg-red-100 text-red-800' :
                  target.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {target.priority}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Pending Review */}
      {pendingReview.length > 0 && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Clues Pending Review</h3>
          <div className="space-y-4">
            {pendingReview.map((clue) => (
              <div key={clue.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900">{clue.type.charAt(0).toUpperCase() + clue.type.slice(1)}</h4>
                    <p className="text-sm text-gray-600">Source: {clue.source} | Confidence: {(clue.confidence * 100).toFixed(0)}%</p>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => approveClue(clue.id)}
                      className="p-2 text-green-600 hover:bg-green-50 rounded"
                      title="Approve"
                    >
                      <Check className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => rejectClue(clue.id)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded"
                      title="Reject"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <p className="text-sm text-gray-700 mb-2">{clue.content}</p>
                
                {clue.keywords.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {clue.keywords.map((keyword, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                        {keyword}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Audit Log */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Log</h3>
        <div className="space-y-2 max-h-64 overflow-y-auto">
          {auditLog.map((entry) => (
            <div key={entry.id} className="flex items-start space-x-3 p-2 text-sm">
              <span className="text-gray-500 text-xs whitespace-nowrap">
                {formatTimestamp(entry.timestamp)}
              </span>
              <span className="text-gray-700">{entry.message}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default AutoMonitoringDashboard;
