import React, { useState, useEffect } from 'react'
import { Browser<PERSON>outer as Router, Routes, Route } from 'react-router-dom'
import Header from './components/Header'
import Dashboard from './components/Dashboard'
import MapView from './components/MapView'
import ClueAnalysis from './components/ClueAnalysis'
import HistoricalData from './components/HistoricalData'
import TrailDatabase from './components/TrailDatabase'
import DataCollectionDashboard from './components/DataCollectionDashboard'
import AutoMonitoringDashboard from './components/AutoMonitoringDashboard'
import { TrailProvider } from './context/TrailContext'
import { ClueProvider } from './context/ClueContext'

function App() {
  return (
    <TrailProvider>
      <ClueProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <Header />
            <main className="container mx-auto px-4 py-8">
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/map" element={<MapView />} />
                <Route path="/clues" element={<ClueAnalysis />} />
                <Route path="/historical" element={<HistoricalData />} />
                <Route path="/trails" element={<TrailDatabase />} />
                <Route path="/data-collection" element={<DataCollectionDashboard />} />
                <Route path="/auto-monitoring" element={<AutoMonitoringDashboard />} />
              </Routes>
            </main>
          </div>
        </Router>
      </ClueProvider>
    </TrailProvider>
  )
}

export default App
