// Advanced pattern recognition engine for treasure hunt analysis
import { historicalTreasures } from '../data/historicalTreasures';

class PatternRecognitionEngine {
  constructor() {
    this.patterns = {
      linguistic: {},
      geographic: {},
      temporal: {},
      behavioral: {}
    };
    this.confidence = {
      high: 0.8,
      medium: 0.6,
      low: 0.4
    };
  }

  /**
   * Analyze all patterns from collected data
   */
  async analyzePatterns(collectedData) {
    const analysis = {
      linguisticPatterns: await this.analyzeLinguisticPatterns(collectedData),
      geographicPatterns: await this.analyzeGeographicPatterns(collectedData),
      temporalPatterns: await this.analyzeTemporalPatterns(collectedData),
      behavioralPatterns: await this.analyzeBehavioralPatterns(collectedData),
      organizerPreferences: await this.analyzeOrganizerPreferences(collectedData)
    };

    // Calculate pattern confidence scores
    analysis.confidenceScores = this.calculatePatternConfidence(analysis);
    
    return analysis;
  }

  /**
   * Analyze linguistic patterns in clues and riddles
   */
  async analyzeLinguisticPatterns(data) {
    const patterns = {
      languageUsage: {},
      riddleStructure: {},
      keywordFrequency: {},
      culturalReferences: {},
      difficultyProgression: {}
    };

    // Analyze riddles and hints by year
    const textByYear = this.groupByYear([...data.riddles, ...data.hints]);
    
    for (const [year, items] of Object.entries(textByYear)) {
      patterns.languageUsage[year] = this.analyzeLanguageUsage(items);
      patterns.riddleStructure[year] = this.analyzeRiddleStructure(items);
      patterns.keywordFrequency[year] = this.analyzeKeywordFrequency(items);
      patterns.culturalReferences[year] = this.analyzeCulturalReferences(items);
    }

    // Analyze difficulty progression
    patterns.difficultyProgression = this.analyzeDifficultyProgression(textByYear);

    return patterns;
  }

  /**
   * Analyze geographic clustering and location patterns
   */
  async analyzeGeographicPatterns(data) {
    const patterns = {
      locationClustering: {},
      elevationPreferences: {},
      trailTypePreferences: {},
      accessibilityPatterns: {},
      proximityPatterns: {}
    };

    // Analyze historical treasure locations
    patterns.locationClustering = this.analyzeLocationClustering(historicalTreasures);
    patterns.elevationPreferences = this.analyzeElevationPreferences(historicalTreasures);
    patterns.trailTypePreferences = this.analyzeTrailTypePreferences(historicalTreasures);
    patterns.accessibilityPatterns = this.analyzeAccessibilityPatterns(historicalTreasures);
    patterns.proximityPatterns = this.analyzeProximityPatterns(historicalTreasures);

    return patterns;
  }

  /**
   * Analyze temporal patterns and timing preferences
   */
  async analyzeTemporalPatterns(data) {
    const patterns = {
      seasonalTiming: {},
      huntDuration: {},
      hintReleasePattern: {},
      solutionTiming: {}
    };

    patterns.seasonalTiming = this.analyzeSeasonalTiming(historicalTreasures);
    patterns.huntDuration = this.analyzeHuntDuration(historicalTreasures);
    patterns.hintReleasePattern = this.analyzeHintReleasePattern(data.hints);
    patterns.solutionTiming = this.analyzeSolutionTiming(data.solutions);

    return patterns;
  }

  /**
   * Analyze organizer behavioral patterns
   */
  async analyzeBehavioralPatterns(data) {
    const patterns = {
      communicationStyle: {},
      hintingStrategy: {},
      difficultyAdjustment: {},
      communityInteraction: {}
    };

    patterns.communicationStyle = this.analyzeCommunicationStyle(data.interviews);
    patterns.hintingStrategy = this.analyzeHintingStrategy(data.hints);
    patterns.difficultyAdjustment = this.analyzeDifficultyAdjustment(data);
    patterns.communityInteraction = this.analyzeCommunityInteraction(data);

    return patterns;
  }

  /**
   * Analyze organizer preferences from interviews and statements
   */
  async analyzeOrganizerPreferences(data) {
    const preferences = {
      locationCriteria: [],
      avoidanceFactors: [],
      safetyConsiderations: [],
      accessibilityRequirements: [],
      aestheticPreferences: []
    };

    // Extract preferences from interview insights
    data.interviews.forEach(interview => {
      if (interview.insights) {
        interview.insights.forEach(insight => {
          this.categorizeInsight(insight, preferences);
        });
      }
    });

    return preferences;
  }

  /**
   * Group data items by year
   */
  groupByYear(items) {
    return items.reduce((acc, item) => {
      const year = item.year || 'unknown';
      if (!acc[year]) acc[year] = [];
      acc[year].push(item);
      return acc;
    }, {});
  }

  /**
   * Analyze language usage patterns
   */
  analyzeLanguageUsage(items) {
    const usage = {
      languages: { english: 0, spanish: 0, other: 0 },
      complexity: { simple: 0, moderate: 0, complex: 0 },
      tone: { formal: 0, casual: 0, playful: 0 }
    };

    items.forEach(item => {
      if (!item.text) return;
      
      // Language detection
      if (/[áéíóúñ]/.test(item.text) || /si sufres|busca el/i.test(item.text)) {
        usage.languages.spanish++;
      } else {
        usage.languages.english++;
      }

      // Complexity analysis
      const wordCount = item.text.split(' ').length;
      if (wordCount < 10) usage.complexity.simple++;
      else if (wordCount < 30) usage.complexity.moderate++;
      else usage.complexity.complex++;

      // Tone analysis
      if (/\b(shall|must|will)\b/i.test(item.text)) usage.tone.formal++;
      else if (/[!?]{2,}|lol|haha/i.test(item.text)) usage.tone.playful++;
      else usage.tone.casual++;
    });

    return usage;
  }

  /**
   * Analyze riddle structure patterns
   */
  analyzeRiddleStructure(items) {
    const structure = {
      lineCount: [],
      rhymeScheme: [],
      metaphorUsage: 0,
      directionalClues: 0,
      numericalClues: 0
    };

    items.forEach(item => {
      if (!item.text) return;
      
      const lines = item.text.split('\n').filter(line => line.trim());
      structure.lineCount.push(lines.length);

      if (/\b(like|as|metaphor)\b/i.test(item.text)) {
        structure.metaphorUsage++;
      }

      if (/\b(north|south|east|west|up|down|left|right)\b/i.test(item.text)) {
        structure.directionalClues++;
      }

      if (/\d+/.test(item.text)) {
        structure.numericalClues++;
      }
    });

    return structure;
  }

  /**
   * Analyze keyword frequency patterns
   */
  analyzeKeywordFrequency(items) {
    const frequency = {};
    
    items.forEach(item => {
      if (item.keywords) {
        item.keywords.forEach(keyword => {
          frequency[keyword] = (frequency[keyword] || 0) + 1;
        });
      }
    });

    // Sort by frequency
    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .reduce((acc, [keyword, count]) => {
        acc[keyword] = count;
        return acc;
      }, {});
  }

  /**
   * Analyze cultural references
   */
  analyzeCulturalReferences(items) {
    const references = {
      movies: [],
      books: [],
      historical: [],
      religious: [],
      local: []
    };

    const patterns = {
      movies: /goonies|indiana jones|treasure|pirates/i,
      books: /literature|novel|story/i,
      historical: /pioneer|mining|historic|heritage/i,
      religious: /church|cathedral|temple|chapel/i,
      local: /utah|salt lake|wasatch|mormon/i
    };

    items.forEach(item => {
      if (!item.text) return;
      
      Object.entries(patterns).forEach(([category, pattern]) => {
        if (pattern.test(item.text)) {
          references[category].push(item.text);
        }
      });
    });

    return references;
  }

  /**
   * Analyze location clustering patterns
   */
  analyzeLocationClustering(treasures) {
    const clusters = {
      wasatchFront: 0,
      saltLakeValley: 0,
      northernUtah: 0,
      centralUtah: 0,
      averageDistance: 0
    };

    let totalDistance = 0;
    const center = [40.7589, -111.8883]; // Salt Lake City center

    treasures.forEach(treasure => {
      if (treasure.characteristics.wasatchFront) {
        clusters.wasatchFront++;
      }

      // Calculate distance from Salt Lake City
      const distance = this.calculateDistance(
        center[0], center[1],
        treasure.coordinates[0], treasure.coordinates[1]
      );
      totalDistance += distance;

      // Categorize by region
      if (treasure.coordinates[0] > 41.0) clusters.northernUtah++;
      else if (treasure.coordinates[0] > 40.0) clusters.saltLakeValley++;
      else clusters.centralUtah++;
    });

    clusters.averageDistance = totalDistance / treasures.length;

    return clusters;
  }

  /**
   * Calculate distance between two coordinates
   */
  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 3959; // Earth's radius in miles
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  toRadians(degrees) {
    return degrees * (Math.PI / 180);
  }

  /**
   * Analyze elevation preferences
   */
  analyzeElevationPreferences(treasures) {
    const elevations = treasures.map(t => t.elevation);
    return {
      min: Math.min(...elevations),
      max: Math.max(...elevations),
      average: elevations.reduce((a, b) => a + b, 0) / elevations.length,
      median: this.calculateMedian(elevations),
      distribution: this.createElevationDistribution(elevations)
    };
  }

  calculateMedian(numbers) {
    const sorted = [...numbers].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 
      ? (sorted[mid - 1] + sorted[mid]) / 2 
      : sorted[mid];
  }

  createElevationDistribution(elevations) {
    const ranges = {
      '4000-5000': 0,
      '5000-6000': 0,
      '6000-7000': 0,
      '7000-8000': 0,
      '8000+': 0
    };

    elevations.forEach(elevation => {
      if (elevation < 5000) ranges['4000-5000']++;
      else if (elevation < 6000) ranges['5000-6000']++;
      else if (elevation < 7000) ranges['6000-7000']++;
      else if (elevation < 8000) ranges['7000-8000']++;
      else ranges['8000+']++;
    });

    return ranges;
  }

  /**
   * Calculate pattern confidence scores
   */
  calculatePatternConfidence(analysis) {
    const scores = {};
    
    // Geographic patterns have highest confidence (based on actual locations)
    scores.geographic = this.confidence.high;
    
    // Temporal patterns have medium confidence (limited sample size)
    scores.temporal = this.confidence.medium;
    
    // Linguistic patterns depend on data quality
    scores.linguistic = this.confidence.medium;
    
    // Behavioral patterns have lower confidence (interpretation-based)
    scores.behavioral = this.confidence.low;

    return scores;
  }

  /**
   * Categorize organizer insights
   */
  categorizeInsight(insight, preferences) {
    const categories = {
      location: /location|place|area|spot|site/i,
      safety: /safe|danger|risk|emergency|cell|coverage/i,
      access: /access|reach|hike|walk|drive|park/i,
      aesthetic: /beautiful|scenic|view|pretty|gorgeous/i,
      avoid: /not|never|avoid|don't|won't/i
    };

    Object.entries(categories).forEach(([category, pattern]) => {
      if (pattern.test(insight)) {
        switch (category) {
          case 'location':
            preferences.locationCriteria.push(insight);
            break;
          case 'safety':
            preferences.safetyConsiderations.push(insight);
            break;
          case 'access':
            preferences.accessibilityRequirements.push(insight);
            break;
          case 'aesthetic':
            preferences.aestheticPreferences.push(insight);
            break;
          case 'avoid':
            preferences.avoidanceFactors.push(insight);
            break;
        }
      }
    });
  }

  // Additional analysis methods would be implemented here...
  // (Continuing with other pattern analysis methods)
}

export default new PatternRecognitionEngine();
