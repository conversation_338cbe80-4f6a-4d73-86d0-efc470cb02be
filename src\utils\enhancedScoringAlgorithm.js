// Enhanced scoring algorithm with pattern-based improvements
import { scoringWeights } from '../data/utahTrails';

class EnhancedScoringAlgorithm {
  constructor() {
    // Original weights (validated against historical data)
    this.baseWeights = {
      wasatchFront: 0.30,      // Reduced from 40% to make room for new factors
      elevationRange: 0.20,    // Reduced from 25%
      establishedTrail: 0.15,  // Reduced from 20%
      hikeTimeRange: 0.10      // Reduced from 15%
    };

    // New pattern-based weights (25% total)
    this.patternWeights = {
      organizerPreference: 0.08,    // Based on interview analysis
      linguisticMatch: 0.06,        // Clue language pattern matching
      geographicClustering: 0.05,   // Proximity to historical finds
      temporalPattern: 0.03,        // Seasonal and timing patterns
      communityConsensus: 0.03      // Community theory alignment
    };

    this.totalWeight = 1.0; // All weights sum to 100%
    this.patterns = null;   // Will be loaded from pattern analysis
  }

  /**
   * Load pattern analysis results
   */
  loadPatterns(patternAnalysis) {
    this.patterns = patternAnalysis;
  }

  /**
   * Calculate enhanced trail score with pattern integration
   */
  calculateEnhancedScore(trail, clues = [], communityTheories = []) {
    const baseScore = this.calculateBaseScore(trail);
    const patternScore = this.calculatePatternScore(trail, clues, communityTheories);
    
    const enhancedScore = {
      base: baseScore,
      patterns: patternScore,
      total: Math.min(baseScore.total + patternScore.total, 100),
      confidence: this.calculateConfidence(trail, baseScore, patternScore),
      breakdown: {
        ...baseScore.breakdown,
        ...patternScore.breakdown
      }
    };

    return enhancedScore;
  }

  /**
   * Calculate base score using original validated algorithm
   */
  calculateBaseScore(trail) {
    const scores = {
      wasatchFront: 0,
      elevationRange: 0,
      establishedTrail: 0,
      hikeTimeRange: 0
    };

    // Wasatch Front proximity
    scores.wasatchFront = trail.wasatchFront ? 100 : 0;

    // Elevation range 4,500-7,000 feet
    if (trail.elevation >= 4500 && trail.elevation <= 7000) {
      scores.elevationRange = 100;
    } else if (trail.elevation >= 4000 && trail.elevation <= 8000) {
      scores.elevationRange = 60;
    } else {
      scores.elevationRange = 20;
    }

    // Established trail system
    scores.establishedTrail = trail.establishedTrail ? 100 : 0;

    // 30-90 minute hike accessibility
    if (trail.hikeTime >= 30 && trail.hikeTime <= 90) {
      scores.hikeTimeRange = 100;
    } else if (trail.hikeTime >= 15 && trail.hikeTime <= 120) {
      scores.hikeTimeRange = 70;
    } else if (trail.hikeTime >= 0 && trail.hikeTime <= 180) {
      scores.hikeTimeRange = 40;
    } else {
      scores.hikeTimeRange = 10;
    }

    const total = (
      scores.wasatchFront * this.baseWeights.wasatchFront +
      scores.elevationRange * this.baseWeights.elevationRange +
      scores.establishedTrail * this.baseWeights.establishedTrail +
      scores.hikeTimeRange * this.baseWeights.hikeTimeRange
    );

    return {
      total: total,
      breakdown: scores
    };
  }

  /**
   * Calculate pattern-based score enhancements
   */
  calculatePatternScore(trail, clues, communityTheories) {
    const scores = {
      organizerPreference: 0,
      linguisticMatch: 0,
      geographicClustering: 0,
      temporalPattern: 0,
      communityConsensus: 0
    };

    if (this.patterns) {
      scores.organizerPreference = this.calculateOrganizerPreferenceScore(trail);
      scores.linguisticMatch = this.calculateLinguisticMatchScore(trail, clues);
      scores.geographicClustering = this.calculateGeographicClusteringScore(trail);
      scores.temporalPattern = this.calculateTemporalPatternScore(trail);
      scores.communityConsensus = this.calculateCommunityConsensusScore(trail, communityTheories);
    }

    const total = (
      scores.organizerPreference * this.patternWeights.organizerPreference +
      scores.linguisticMatch * this.patternWeights.linguisticMatch +
      scores.geographicClustering * this.patternWeights.geographicClustering +
      scores.temporalPattern * this.patternWeights.temporalPattern +
      scores.communityConsensus * this.patternWeights.communityConsensus
    ) * 100; // Convert to 0-25 point scale

    return {
      total: total,
      breakdown: scores
    };
  }

  /**
   * Score based on organizer stated preferences
   */
  calculateOrganizerPreferenceScore(trail) {
    if (!this.patterns?.organizerPreferences) return 50;

    let score = 50; // Base score
    const prefs = this.patterns.organizerPreferences;

    // Check location criteria matches
    prefs.locationCriteria.forEach(criteria => {
      if (this.trailMatchesCriteria(trail, criteria)) {
        score += 10;
      }
    });

    // Check avoidance factors
    prefs.avoidanceFactors.forEach(factor => {
      if (this.trailMatchesCriteria(trail, factor)) {
        score -= 15; // Penalty for matching avoidance factors
      }
    });

    // Safety considerations
    prefs.safetyConsiderations.forEach(safety => {
      if (this.trailMatchesCriteria(trail, safety)) {
        score += 5;
      }
    });

    // Accessibility requirements
    prefs.accessibilityRequirements.forEach(access => {
      if (this.trailMatchesCriteria(trail, access)) {
        score += 8;
      }
    });

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Score based on linguistic pattern matching with clues
   */
  calculateLinguisticMatchScore(trail, clues) {
    if (!clues || clues.length === 0) return 50;

    let totalMatches = 0;
    let totalPossible = 0;

    clues.forEach(clue => {
      if (clue.keywords) {
        const matches = this.countKeywordMatches(trail, clue.keywords);
        totalMatches += matches;
        totalPossible += clue.keywords.length;
      }
    });

    if (totalPossible === 0) return 50;

    const matchRatio = totalMatches / totalPossible;
    return Math.min(100, matchRatio * 100 + 20); // Boost base score
  }

  /**
   * Score based on geographic clustering with historical finds
   */
  calculateGeographicClusteringScore(trail) {
    if (!this.patterns?.geographicPatterns) return 50;

    const clustering = this.patterns.geographicPatterns.locationClustering;
    let score = 50;

    // Distance from historical treasure locations
    const historicalLocations = [
      [40.6089, -111.7910], // Rocky Mouth Canyon
      [40.6847, -111.8398], // Heughs Canyon
      [41.3847, -111.8398], // Ben Lomond Peak
      [40.8847, -111.8398]  // Mueller Park
    ];

    const distances = historicalLocations.map(loc => 
      this.calculateDistance(trail.coordinates[0], trail.coordinates[1], loc[0], loc[1])
    );

    const minDistance = Math.min(...distances);
    const avgDistance = clustering.averageDistance || 10;

    // Closer to historical finds = higher score
    if (minDistance < avgDistance * 0.5) {
      score += 30;
    } else if (minDistance < avgDistance) {
      score += 15;
    } else if (minDistance < avgDistance * 2) {
      score += 5;
    }

    return Math.min(100, score);
  }

  /**
   * Score based on temporal patterns
   */
  calculateTemporalPatternScore(trail) {
    if (!this.patterns?.temporalPatterns) return 50;

    let score = 50;
    const temporal = this.patterns.temporalPatterns;

    // Current season alignment
    const currentMonth = new Date().getMonth() + 1;
    if (temporal.seasonalTiming) {
      // Summer months (June-August) are preferred based on historical data
      if (currentMonth >= 6 && currentMonth <= 8) {
        score += 20;
      }
    }

    // Trail accessibility in current season
    if (trail.elevation < 7000) {
      score += 10; // Lower elevation trails accessible year-round
    }

    return Math.min(100, score);
  }

  /**
   * Score based on community theory consensus
   */
  calculateCommunityConsensusScore(trail, communityTheories) {
    if (!communityTheories || communityTheories.length === 0) return 50;

    let mentions = 0;
    let totalTheories = communityTheories.length;

    communityTheories.forEach(theory => {
      if (theory.location && this.trailMatchesLocation(trail, theory.location)) {
        mentions++;
      }
      if (theory.reasoning && this.trailMatchesCriteria(trail, theory.reasoning)) {
        mentions += 0.5;
      }
    });

    if (totalTheories === 0) return 50;

    const consensusRatio = mentions / totalTheories;
    return Math.min(100, consensusRatio * 80 + 20);
  }

  /**
   * Calculate overall confidence score
   */
  calculateConfidence(trail, baseScore, patternScore) {
    let confidence = 0.5; // Base confidence

    // Base score confidence (historical validation)
    if (baseScore.total > 80) confidence += 0.3;
    else if (baseScore.total > 60) confidence += 0.2;
    else if (baseScore.total > 40) confidence += 0.1;

    // Pattern score confidence
    if (this.patterns) {
      confidence += 0.1; // Having pattern data increases confidence
      
      if (patternScore.total > 15) confidence += 0.1;
      if (patternScore.breakdown.organizerPreference > 70) confidence += 0.05;
      if (patternScore.breakdown.geographicClustering > 70) confidence += 0.05;
    }

    return Math.min(1.0, confidence);
  }

  /**
   * Helper methods
   */
  trailMatchesCriteria(trail, criteria) {
    const searchText = `
      ${trail.name} 
      ${trail.location} 
      ${trail.trailType || ''} 
      ${trail.features?.join(' ') || ''} 
      ${trail.description || ''}
    `.toLowerCase();

    return searchText.includes(criteria.toLowerCase());
  }

  trailMatchesLocation(trail, location) {
    return trail.name.toLowerCase().includes(location.toLowerCase()) ||
           trail.location.toLowerCase().includes(location.toLowerCase());
  }

  countKeywordMatches(trail, keywords) {
    const searchText = `
      ${trail.name} 
      ${trail.location} 
      ${trail.trailType || ''} 
      ${trail.features?.join(' ') || ''} 
      ${trail.description || ''}
    `.toLowerCase();

    return keywords.filter(keyword => 
      searchText.includes(keyword.toLowerCase())
    ).length;
  }

  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 3959; // Earth's radius in miles
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  toRadians(degrees) {
    return degrees * (Math.PI / 180);
  }

  /**
   * Get scoring weights for transparency
   */
  getScoringWeights() {
    return {
      base: this.baseWeights,
      patterns: this.patternWeights,
      total: { ...this.baseWeights, ...this.patternWeights }
    };
  }

  /**
   * Validate enhanced algorithm against historical data
   */
  validateAlgorithm(historicalTreasures, patterns) {
    const results = {
      accuracy: 0,
      improvements: {},
      recommendations: []
    };

    // Test algorithm against known treasure locations
    historicalTreasures.forEach(treasure => {
      // Create mock trail object from treasure data
      const mockTrail = {
        name: treasure.location,
        coordinates: treasure.coordinates,
        elevation: treasure.elevation,
        hikeTime: treasure.hikeTime,
        wasatchFront: treasure.characteristics.wasatchFront,
        establishedTrail: treasure.characteristics.establishedTrail
      };

      const score = this.calculateEnhancedScore(mockTrail, [], []);
      
      // High scores for actual treasure locations indicate good algorithm
      if (score.total > 70) {
        results.accuracy += 0.25; // 25% per correct high score
      }
    });

    return results;
  }
}

export default new EnhancedScoringAlgorithm();
