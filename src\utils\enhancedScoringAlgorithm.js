// Enhanced scoring algorithm with ML/AI integration
import { scoringWeights } from '../data/utahTrails';
import AdvancedMLService from '../services/advancedMLService.js';

class EnhancedScoringAlgorithm {
  constructor() {
    // Original weights (validated against historical data)
    this.baseWeights = {
      wasatchFront: 0.30,      // Reduced from 40% to make room for new factors
      elevationRange: 0.20,    // Reduced from 25%
      establishedTrail: 0.15,  // Reduced from 20%
      hikeTimeRange: 0.10      // Reduced from 15%
    };

    // New pattern-based weights (25% total)
    this.patternWeights = {
      organizerPreference: 0.08,    // Based on interview analysis
      linguisticMatch: 0.06,        // Clue language pattern matching
      geographicClustering: 0.05,   // Proximity to historical finds
      temporalPattern: 0.03,        // Seasonal and timing patterns
      communityConsensus: 0.03      // Community theory alignment
    };

    this.totalWeight = 1.0; // All weights sum to 100%
    this.patterns = null;   // Will be loaded from pattern analysis

    // ML Service Integration
    this.mlService = new AdvancedMLService();
    this.mlEnabled = false;
    this.fallbackMode = false;
    this.performanceMetrics = {
      accuracy: 0,
      processingTime: 0,
      memoryUsage: 0,
      lastUpdate: null
    };
  }

  /**
   * Load pattern analysis results
   */
  loadPatterns(patternAnalysis) {
    this.patterns = patternAnalysis;
  }

  /**
   * Initialize ML services with historical data
   * @param {Object} historicalData - Training data for ML models
   * @returns {Promise<boolean>} - Success status
   */
  async initializeMLServices(historicalData) {
    try {
      console.log('Initializing ML services for enhanced scoring...');
      const startTime = performance.now();

      const initResult = await this.mlService.initialize(historicalData);

      if (initResult.success) {
        this.mlEnabled = true;
        this.fallbackMode = false;

        const endTime = performance.now();
        this.performanceMetrics.processingTime = endTime - startTime;
        this.performanceMetrics.lastUpdate = new Date().toISOString();

        console.log('ML services initialized successfully:', initResult);
        return true;
      } else {
        console.warn('ML initialization failed, using fallback mode');
        this.fallbackMode = true;
        return false;
      }
    } catch (error) {
      console.error('ML initialization error:', error);
      this.fallbackMode = true;
      this.mlEnabled = false;
      return false;
    }
  }

  /**
   * Check if ML services are available and healthy
   * @returns {boolean} - ML availability status
   */
  isMLAvailable() {
    return this.mlEnabled && !this.fallbackMode && this.mlService.isInitialized;
  }

  /**
   * Calculate enhanced trail score with ML/AI integration
   */
  async calculateEnhancedScore(trail, clues = [], communityTheories = []) {
    const startTime = performance.now();

    try {
      // Use ML-powered scoring if available
      if (this.isMLAvailable()) {
        const mlScore = await this.calculateMLEnhancedScore(trail, clues, communityTheories);

        const endTime = performance.now();
        this.performanceMetrics.processingTime = endTime - startTime;

        return mlScore;
      } else {
        // Fallback to pattern-based scoring
        console.log('Using fallback pattern-based scoring');
        return this.calculatePatternBasedScore(trail, clues, communityTheories);
      }
    } catch (error) {
      console.error('Enhanced scoring error:', error);
      // Emergency fallback to base scoring
      return this.calculateEmergencyFallbackScore(trail, error);
    }
  }

  /**
   * Calculate ML-enhanced score using advanced algorithms
   */
  async calculateMLEnhancedScore(trail, clues, communityTheories) {
    try {
      const mlResult = await this.mlService.calculateEnhancedScore(trail, clues, communityTheories);

      // Convert ML result to expected format
      const enhancedScore = {
        total: mlResult.finalScore,
        confidence: mlResult.confidence,
        uncertainty: mlResult.uncertainty,
        credibleInterval: mlResult.credibleInterval,

        // Component breakdowns
        base: {
          total: mlResult.componentAnalyses.ensemble.modelContributions
            .find(m => m.model === 'Base Algorithm')?.score || 0,
          breakdown: this.extractBaseBreakdown(mlResult)
        },

        ml: {
          total: mlResult.finalScore,
          breakdown: {
            ensemble: mlResult.componentAnalyses.ensemble.score,
            nlp: mlResult.componentAnalyses.nlp.score,
            geospatial: mlResult.componentAnalyses.geospatial.score,
            temporal: mlResult.componentAnalyses.temporal.score,
            anomaly: mlResult.componentAnalyses.anomaly.score
          }
        },

        // ML-specific insights
        mlInsights: {
          modelContributions: mlResult.componentAnalyses.ensemble.modelContributions,
          nlpAnalysis: mlResult.componentAnalyses.nlp,
          geospatialAnalysis: mlResult.componentAnalyses.geospatial,
          temporalAnalysis: mlResult.componentAnalyses.temporal,
          anomalyDetection: mlResult.componentAnalyses.anomaly,
          recommendations: mlResult.recommendations
        },

        metadata: {
          ...mlResult.metadata,
          algorithm: 'ML-Enhanced',
          processingTime: this.performanceMetrics.processingTime,
          mlEnabled: true
        }
      };

      // Update performance metrics
      this.updatePerformanceMetrics(enhancedScore);

      return enhancedScore;

    } catch (error) {
      console.error('ML scoring failed:', error);
      throw error;
    }
  }

  /**
   * Calculate pattern-based score (fallback method)
   */
  calculatePatternBasedScore(trail, clues, communityTheories) {
    const baseScore = this.calculateBaseScore(trail);
    const patternScore = this.calculatePatternScore(trail, clues, communityTheories);

    const enhancedScore = {
      base: baseScore,
      patterns: patternScore,
      total: Math.min(baseScore.total + patternScore.total, 100),
      confidence: this.calculateConfidence(trail, baseScore, patternScore),
      uncertainty: this.calculateUncertainty(baseScore, patternScore),
      credibleInterval: this.calculateCredibleInterval(baseScore.total + patternScore.total),

      breakdown: {
        ...baseScore.breakdown,
        ...patternScore.breakdown
      },

      metadata: {
        algorithm: 'Pattern-Based',
        mlEnabled: false,
        fallbackMode: this.fallbackMode,
        timestamp: new Date().toISOString()
      }
    };

    return enhancedScore;
  }

  /**
   * Emergency fallback to base scoring
   */
  calculateEmergencyFallbackScore(trail, error) {
    console.warn('Using emergency fallback scoring due to error:', error);

    const baseScore = this.calculateBaseScore(trail);

    return {
      total: baseScore.total,
      confidence: 0.5, // Lower confidence due to fallback
      uncertainty: 20, // Higher uncertainty
      credibleInterval: {
        lower: Math.max(0, baseScore.total - 20),
        upper: Math.min(100, baseScore.total + 20),
        width: 40
      },

      base: baseScore,

      metadata: {
        algorithm: 'Emergency-Fallback',
        mlEnabled: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * Calculate base score using original validated algorithm
   */
  calculateBaseScore(trail) {
    const scores = {
      wasatchFront: 0,
      elevationRange: 0,
      establishedTrail: 0,
      hikeTimeRange: 0
    };

    // Wasatch Front proximity
    scores.wasatchFront = trail.wasatchFront ? 100 : 0;

    // Elevation range 4,500-7,000 feet
    if (trail.elevation >= 4500 && trail.elevation <= 7000) {
      scores.elevationRange = 100;
    } else if (trail.elevation >= 4000 && trail.elevation <= 8000) {
      scores.elevationRange = 60;
    } else {
      scores.elevationRange = 20;
    }

    // Established trail system
    scores.establishedTrail = trail.establishedTrail ? 100 : 0;

    // 30-90 minute hike accessibility
    if (trail.hikeTime >= 30 && trail.hikeTime <= 90) {
      scores.hikeTimeRange = 100;
    } else if (trail.hikeTime >= 15 && trail.hikeTime <= 120) {
      scores.hikeTimeRange = 70;
    } else if (trail.hikeTime >= 0 && trail.hikeTime <= 180) {
      scores.hikeTimeRange = 40;
    } else {
      scores.hikeTimeRange = 10;
    }

    const total = (
      scores.wasatchFront * this.baseWeights.wasatchFront +
      scores.elevationRange * this.baseWeights.elevationRange +
      scores.establishedTrail * this.baseWeights.establishedTrail +
      scores.hikeTimeRange * this.baseWeights.hikeTimeRange
    );

    return {
      total: total,
      breakdown: scores
    };
  }

  /**
   * Calculate pattern-based score enhancements
   */
  calculatePatternScore(trail, clues, communityTheories) {
    const scores = {
      organizerPreference: 0,
      linguisticMatch: 0,
      geographicClustering: 0,
      temporalPattern: 0,
      communityConsensus: 0
    };

    if (this.patterns) {
      scores.organizerPreference = this.calculateOrganizerPreferenceScore(trail);
      scores.linguisticMatch = this.calculateLinguisticMatchScore(trail, clues);
      scores.geographicClustering = this.calculateGeographicClusteringScore(trail);
      scores.temporalPattern = this.calculateTemporalPatternScore(trail);
      scores.communityConsensus = this.calculateCommunityConsensusScore(trail, communityTheories);
    }

    const total = (
      scores.organizerPreference * this.patternWeights.organizerPreference +
      scores.linguisticMatch * this.patternWeights.linguisticMatch +
      scores.geographicClustering * this.patternWeights.geographicClustering +
      scores.temporalPattern * this.patternWeights.temporalPattern +
      scores.communityConsensus * this.patternWeights.communityConsensus
    ) * 100; // Convert to 0-25 point scale

    return {
      total: total,
      breakdown: scores
    };
  }

  /**
   * Score based on organizer stated preferences
   */
  calculateOrganizerPreferenceScore(trail) {
    if (!this.patterns?.organizerPreferences) return 50;

    let score = 50; // Base score
    const prefs = this.patterns.organizerPreferences;

    // Check location criteria matches
    prefs.locationCriteria.forEach(criteria => {
      if (this.trailMatchesCriteria(trail, criteria)) {
        score += 10;
      }
    });

    // Check avoidance factors
    prefs.avoidanceFactors.forEach(factor => {
      if (this.trailMatchesCriteria(trail, factor)) {
        score -= 15; // Penalty for matching avoidance factors
      }
    });

    // Safety considerations
    prefs.safetyConsiderations.forEach(safety => {
      if (this.trailMatchesCriteria(trail, safety)) {
        score += 5;
      }
    });

    // Accessibility requirements
    prefs.accessibilityRequirements.forEach(access => {
      if (this.trailMatchesCriteria(trail, access)) {
        score += 8;
      }
    });

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Score based on linguistic pattern matching with clues
   */
  calculateLinguisticMatchScore(trail, clues) {
    if (!clues || clues.length === 0) return 50;

    let totalMatches = 0;
    let totalPossible = 0;

    clues.forEach(clue => {
      if (clue.keywords) {
        const matches = this.countKeywordMatches(trail, clue.keywords);
        totalMatches += matches;
        totalPossible += clue.keywords.length;
      }
    });

    if (totalPossible === 0) return 50;

    const matchRatio = totalMatches / totalPossible;
    return Math.min(100, matchRatio * 100 + 20); // Boost base score
  }

  /**
   * Score based on geographic clustering with historical finds
   */
  calculateGeographicClusteringScore(trail) {
    if (!this.patterns?.geographicPatterns) return 50;

    const clustering = this.patterns.geographicPatterns.locationClustering;
    let score = 50;

    // Distance from historical treasure locations
    const historicalLocations = [
      [40.6089, -111.7910], // Rocky Mouth Canyon
      [40.6847, -111.8398], // Heughs Canyon
      [41.3847, -111.8398], // Ben Lomond Peak
      [40.8847, -111.8398]  // Mueller Park
    ];

    const distances = historicalLocations.map(loc => 
      this.calculateDistance(trail.coordinates[0], trail.coordinates[1], loc[0], loc[1])
    );

    const minDistance = Math.min(...distances);
    const avgDistance = clustering.averageDistance || 10;

    // Closer to historical finds = higher score
    if (minDistance < avgDistance * 0.5) {
      score += 30;
    } else if (minDistance < avgDistance) {
      score += 15;
    } else if (minDistance < avgDistance * 2) {
      score += 5;
    }

    return Math.min(100, score);
  }

  /**
   * Score based on temporal patterns
   */
  calculateTemporalPatternScore(trail) {
    if (!this.patterns?.temporalPatterns) return 50;

    let score = 50;
    const temporal = this.patterns.temporalPatterns;

    // Current season alignment
    const currentMonth = new Date().getMonth() + 1;
    if (temporal.seasonalTiming) {
      // Summer months (June-August) are preferred based on historical data
      if (currentMonth >= 6 && currentMonth <= 8) {
        score += 20;
      }
    }

    // Trail accessibility in current season
    if (trail.elevation < 7000) {
      score += 10; // Lower elevation trails accessible year-round
    }

    return Math.min(100, score);
  }

  /**
   * Score based on community theory consensus
   */
  calculateCommunityConsensusScore(trail, communityTheories) {
    if (!communityTheories || communityTheories.length === 0) return 50;

    let mentions = 0;
    let totalTheories = communityTheories.length;

    communityTheories.forEach(theory => {
      if (theory.location && this.trailMatchesLocation(trail, theory.location)) {
        mentions++;
      }
      if (theory.reasoning && this.trailMatchesCriteria(trail, theory.reasoning)) {
        mentions += 0.5;
      }
    });

    if (totalTheories === 0) return 50;

    const consensusRatio = mentions / totalTheories;
    return Math.min(100, consensusRatio * 80 + 20);
  }

  /**
   * Calculate overall confidence score
   */
  calculateConfidence(trail, baseScore, patternScore) {
    let confidence = 0.5; // Base confidence

    // Base score confidence (historical validation)
    if (baseScore.total > 80) confidence += 0.3;
    else if (baseScore.total > 60) confidence += 0.2;
    else if (baseScore.total > 40) confidence += 0.1;

    // Pattern score confidence
    if (this.patterns) {
      confidence += 0.1; // Having pattern data increases confidence
      
      if (patternScore.total > 15) confidence += 0.1;
      if (patternScore.breakdown.organizerPreference > 70) confidence += 0.05;
      if (patternScore.breakdown.geographicClustering > 70) confidence += 0.05;
    }

    return Math.min(1.0, confidence);
  }

  /**
   * Helper methods
   */
  trailMatchesCriteria(trail, criteria) {
    const searchText = `
      ${trail.name} 
      ${trail.location} 
      ${trail.trailType || ''} 
      ${trail.features?.join(' ') || ''} 
      ${trail.description || ''}
    `.toLowerCase();

    return searchText.includes(criteria.toLowerCase());
  }

  trailMatchesLocation(trail, location) {
    return trail.name.toLowerCase().includes(location.toLowerCase()) ||
           trail.location.toLowerCase().includes(location.toLowerCase());
  }

  countKeywordMatches(trail, keywords) {
    const searchText = `
      ${trail.name} 
      ${trail.location} 
      ${trail.trailType || ''} 
      ${trail.features?.join(' ') || ''} 
      ${trail.description || ''}
    `.toLowerCase();

    return keywords.filter(keyword => 
      searchText.includes(keyword.toLowerCase())
    ).length;
  }

  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 3959; // Earth's radius in miles
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  toRadians(degrees) {
    return degrees * (Math.PI / 180);
  }

  /**
   * Get scoring weights for transparency
   */
  getScoringWeights() {
    return {
      base: this.baseWeights,
      patterns: this.patternWeights,
      total: { ...this.baseWeights, ...this.patternWeights }
    };
  }

  /**
   * Extract base algorithm breakdown from ML results
   */
  extractBaseBreakdown(mlResult) {
    // Extract traditional scoring factors from ML analysis
    return {
      wasatchFront: mlResult.componentAnalyses.geospatial?.score > 70 ? 100 : 0,
      elevationRange: this.calculateElevationScore(mlResult.metadata.trailId),
      establishedTrail: 100, // Assume established trails for now
      hikeTimeRange: this.calculateHikeTimeScore(mlResult.metadata.trailId)
    };
  }

  /**
   * Update performance metrics based on scoring results
   */
  updatePerformanceMetrics(enhancedScore) {
    // Estimate memory usage (simplified)
    this.performanceMetrics.memoryUsage = this.estimateMemoryUsage();

    // Update accuracy based on confidence
    this.performanceMetrics.accuracy = enhancedScore.confidence;

    this.performanceMetrics.lastUpdate = new Date().toISOString();
  }

  /**
   * Estimate current memory usage
   */
  estimateMemoryUsage() {
    // Simplified memory estimation
    if (typeof performance !== 'undefined' && performance.memory) {
      return performance.memory.usedJSHeapSize / (1024 * 1024); // MB
    }
    return 25; // Estimated 25MB if performance.memory not available
  }

  /**
   * Calculate uncertainty for pattern-based scoring
   */
  calculateUncertainty(baseScore, patternScore) {
    const baseUncertainty = 15; // Base uncertainty
    const patternUncertainty = this.patterns ? 10 : 20; // Lower if patterns available

    return Math.sqrt(baseUncertainty * baseUncertainty + patternUncertainty * patternUncertainty);
  }

  /**
   * Calculate credible interval for pattern-based scoring
   */
  calculateCredibleInterval(score) {
    const uncertainty = 15; // Standard uncertainty

    return {
      lower: Math.max(0, score - uncertainty),
      upper: Math.min(100, score + uncertainty),
      width: 2 * uncertainty
    };
  }

  /**
   * Calculate elevation score for breakdown
   */
  calculateElevationScore(trailId) {
    // This would normally look up trail data
    // For now, return a reasonable default
    return 75;
  }

  /**
   * Calculate hike time score for breakdown
   */
  calculateHikeTimeScore(trailId) {
    // This would normally look up trail data
    // For now, return a reasonable default
    return 80;
  }

  /**
   * Get ML service status and performance metrics
   */
  getMLStatus() {
    return {
      enabled: this.mlEnabled,
      available: this.isMLAvailable(),
      fallbackMode: this.fallbackMode,
      performanceMetrics: this.performanceMetrics,
      serviceStatus: this.mlService.getStatus()
    };
  }

  /**
   * Update ML models with new performance data
   */
  async updateMLModels(performanceData) {
    if (this.isMLAvailable()) {
      try {
        this.mlService.updatePerformance(performanceData);
        console.log('ML models updated with performance data');
      } catch (error) {
        console.error('Failed to update ML models:', error);
      }
    }
  }

  /**
   * Export ML models for persistence
   */
  exportMLModels() {
    if (this.isMLAvailable()) {
      return this.mlService.exportModels();
    }
    return null;
  }

  /**
   * Import ML models from saved data
   */
  async importMLModels(modelData) {
    try {
      this.mlService.importModels(modelData);
      this.mlEnabled = true;
      this.fallbackMode = false;
      console.log('ML models imported successfully');
      return true;
    } catch (error) {
      console.error('Failed to import ML models:', error);
      this.fallbackMode = true;
      return false;
    }
  }

  /**
   * Validate enhanced algorithm against historical data
   */
  async validateAlgorithm(historicalTreasures, patterns) {
    const results = {
      accuracy: 0,
      improvements: {},
      recommendations: [],
      mlValidation: null
    };

    let correctPredictions = 0;
    const validationResults = [];

    // Test algorithm against known treasure locations
    for (const treasure of historicalTreasures) {
      // Create mock trail object from treasure data
      const mockTrail = {
        id: `historical_${treasure.year}`,
        name: treasure.location,
        coordinates: treasure.coordinates,
        elevation: treasure.elevation,
        hikeTime: treasure.hikeTime,
        wasatchFront: treasure.characteristics.wasatchFront,
        establishedTrail: treasure.characteristics.establishedTrail
      };

      try {
        const score = await this.calculateEnhancedScore(mockTrail, [], []);

        validationResults.push({
          year: treasure.year,
          location: treasure.location,
          score: score.total,
          confidence: score.confidence,
          algorithm: score.metadata.algorithm
        });

        // High scores for actual treasure locations indicate good algorithm
        if (score.total > 70) {
          correctPredictions++;
        }
      } catch (error) {
        console.error(`Validation failed for ${treasure.location}:`, error);
      }
    }

    results.accuracy = correctPredictions / historicalTreasures.length;
    results.validationResults = validationResults;

    // ML-specific validation if available
    if (this.isMLAvailable()) {
      results.mlValidation = {
        modelsUsed: this.mlService.getStatus().modelStatus,
        performanceMetrics: this.performanceMetrics,
        averageConfidence: validationResults.reduce((sum, r) => sum + r.confidence, 0) / validationResults.length
      };
    }

    return results;
  }
}

export default new EnhancedScoringAlgorithm();
