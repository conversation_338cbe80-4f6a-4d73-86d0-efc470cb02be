// Advanced Machine Learning Service Integration
// Combines all ML algorithms for enhanced treasure hunting predictions

import BayesianEnsemble from '../algorithms/bayesianEnsemble.js';
import NLPAnalyzer from '../algorithms/nlpAnalyzer.js';
import GeospatialAnalyzer from '../algorithms/geospatialAnalyzer.js';

class AdvancedMLService {
  constructor() {
    this.bayesianEnsemble = new BayesianEnsemble();
    this.nlpAnalyzer = new NLPAnalyzer();
    this.geospatialAnalyzer = new GeospatialAnalyzer();
    
    this.isInitialized = false;
    this.modelPerformance = {
      accuracy: 0,
      confidence: 0,
      lastUpdated: null
    };
    
    this.config = {
      ensembleWeights: {
        baseAlgorithm: 0.4,
        nlpAnalysis: 0.25,
        geospatialAnalysis: 0.25,
        temporalAnalysis: 0.1
      },
      confidenceThresholds: {
        high: 0.8,
        medium: 0.6,
        low: 0.4
      },
      adaptiveLearning: true
    };
  }

  /**
   * Initialize the ML service with historical data
   * @param {Object} historicalData - Training data
   * @returns {Promise<Object>} - Initialization results
   */
  async initialize(historicalData) {
    try {
      console.log('Initializing Advanced ML Service...');
      
      // Initialize NLP Analyzer
      if (historicalData.textDocuments) {
        this.nlpAnalyzer.buildModel(historicalData.textDocuments);
      }
      
      // Initialize Geospatial Analyzer
      if (historicalData.treasureLocations) {
        this.geospatialAnalyzer.addHistoricalLocations(historicalData.treasureLocations);
        await this.geospatialAnalyzer.fitGaussianMixtureModel(2);
        await this.geospatialAnalyzer.fitKernelDensityEstimation();
      }
      
      // Initialize Bayesian Ensemble with base models
      this.initializeBayesianEnsemble();
      
      this.isInitialized = true;
      
      const initResults = {
        success: true,
        modelsInitialized: {
          nlp: !!historicalData.textDocuments,
          geospatial: !!historicalData.treasureLocations,
          ensemble: true
        },
        dataStats: {
          textDocuments: historicalData.textDocuments?.length || 0,
          treasureLocations: historicalData.treasureLocations?.length || 0,
          totalTrainingPoints: (historicalData.textDocuments?.length || 0) + 
                              (historicalData.treasureLocations?.length || 0)
        },
        timestamp: new Date().toISOString()
      };
      
      console.log('ML Service initialized successfully:', initResults);
      return initResults;
      
    } catch (error) {
      console.error('Failed to initialize ML Service:', error);
      throw new Error(`ML Service initialization failed: ${error.message}`);
    }
  }

  /**
   * Initialize Bayesian Ensemble with component models
   */
  initializeBayesianEnsemble() {
    // Add base scoring algorithm
    this.bayesianEnsemble.addModel({
      name: 'BaseAlgorithm',
      predict: (trail, clues) => this.calculateBaseScore(trail)
    }, 0.4, 'Base Algorithm');

    // Add NLP-enhanced scoring
    this.bayesianEnsemble.addModel({
      name: 'NLPEnhanced',
      predict: (trail, clues) => this.calculateNLPEnhancedScore(trail, clues)
    }, 0.25, 'NLP Enhanced');

    // Add geospatial scoring
    this.bayesianEnsemble.addModel({
      name: 'GeospatialModel',
      predict: (trail, clues) => this.calculateGeospatialScore(trail)
    }, 0.25, 'Geospatial Model');

    // Add temporal analysis
    this.bayesianEnsemble.addModel({
      name: 'TemporalModel',
      predict: (trail, clues) => this.calculateTemporalScore(trail)
    }, 0.1, 'Temporal Model');
  }

  /**
   * Calculate enhanced trail score using all ML algorithms
   * @param {Object} trail - Trail object
   * @param {Array} clues - Available clues
   * @param {Array} communityTheories - Community theories
   * @returns {Promise<Object>} - Enhanced prediction
   */
  async calculateEnhancedScore(trail, clues = [], communityTheories = []) {
    if (!this.isInitialized) {
      throw new Error('ML Service not initialized. Call initialize() first.');
    }

    try {
      // Get ensemble prediction
      const ensemblePrediction = this.bayesianEnsemble.predict(trail, clues);
      
      // Get detailed component analyses
      const nlpAnalysis = await this.analyzeTrailWithNLP(trail, clues);
      const geospatialAnalysis = await this.analyzeTrailGeospatially(trail);
      const temporalAnalysis = this.analyzeTemporalFactors(trail);
      
      // Calculate anomaly scores
      const anomalyScore = this.detectAnomalies(trail, {
        nlp: nlpAnalysis,
        geospatial: geospatialAnalysis,
        temporal: temporalAnalysis
      });
      
      // Combine all analyses
      const enhancedScore = {
        finalScore: ensemblePrediction.score,
        confidence: ensemblePrediction.confidence,
        uncertainty: ensemblePrediction.uncertainty,
        credibleInterval: ensemblePrediction.credibleInterval,
        
        componentAnalyses: {
          ensemble: ensemblePrediction,
          nlp: nlpAnalysis,
          geospatial: geospatialAnalysis,
          temporal: temporalAnalysis,
          anomaly: anomalyScore
        },
        
        recommendations: this.generateRecommendations(ensemblePrediction, {
          nlp: nlpAnalysis,
          geospatial: geospatialAnalysis,
          temporal: temporalAnalysis,
          anomaly: anomalyScore
        }),
        
        metadata: {
          trailId: trail.id,
          analysisTimestamp: new Date().toISOString(),
          modelVersions: this.getModelVersions(),
          confidenceLevel: this.getConfidenceLevel(ensemblePrediction.confidence)
        }
      };
      
      return enhancedScore;
      
    } catch (error) {
      console.error('Error calculating enhanced score:', error);
      throw new Error(`Enhanced scoring failed: ${error.message}`);
    }
  }

  /**
   * Analyze trail using NLP techniques
   * @param {Object} trail - Trail object
   * @param {Array} clues - Available clues
   * @returns {Promise<Object>} - NLP analysis results
   */
  async analyzeTrailWithNLP(trail, clues) {
    const trailText = `${trail.name} ${trail.location} ${trail.description || ''} ${trail.features?.join(' ') || ''}`;
    const trailAnalysis = this.nlpAnalyzer.analyzeTreasureHuntRelevance(trailText);
    
    // Analyze clue relevance
    const clueAnalyses = clues.map(clue => {
      const clueAnalysis = this.nlpAnalyzer.analyzeTreasureHuntRelevance(clue.content);
      const similarity = this.calculateSemanticSimilarity(trailText, clue.content);
      
      return {
        clueId: clue.id,
        relevance: clueAnalysis.relevanceScore,
        similarity: similarity,
        matchedTerms: clueAnalysis.treasureHuntTerms,
        confidence: clueAnalysis.confidence
      };
    });
    
    // Calculate overall NLP score
    const baseRelevance = trailAnalysis.relevanceScore;
    const clueBonus = clueAnalyses.reduce((sum, analysis) => 
      sum + (analysis.similarity * analysis.confidence), 0) / Math.max(clueAnalyses.length, 1);
    
    const nlpScore = Math.min(100, (baseRelevance * 60) + (clueBonus * 40));
    
    return {
      score: nlpScore,
      trailRelevance: trailAnalysis,
      clueAnalyses: clueAnalyses,
      semanticMatches: this.findSemanticMatches(trailText, clues),
      confidence: (trailAnalysis.confidence + (clueAnalyses.reduce((sum, a) => sum + a.confidence, 0) / Math.max(clueAnalyses.length, 1))) / 2
    };
  }

  /**
   * Analyze trail using geospatial techniques
   * @param {Object} trail - Trail object
   * @returns {Promise<Object>} - Geospatial analysis results
   */
  async analyzeTrailGeospatially(trail) {
    if (!trail.coordinates) {
      return { score: 50, error: 'No coordinates available' };
    }
    
    const locationAnalysis = this.geospatialAnalyzer.calculateLocationProbability(trail.coordinates);
    
    // Convert probability to score (0-100)
    const gmmScore = Math.min(100, locationAnalysis.gmmProbability * 1000); // Scale up probability
    const kdeScore = Math.min(100, locationAnalysis.kdeProbability * 1000);
    const proximityScore = locationAnalysis.proximityScore.score * 100;
    
    // Weighted combination
    const geospatialScore = (gmmScore * 0.4) + (kdeScore * 0.3) + (proximityScore * 0.3);
    
    return {
      score: geospatialScore,
      gmmProbability: locationAnalysis.gmmProbability,
      kdeProbability: locationAnalysis.kdeProbability,
      proximityAnalysis: locationAnalysis.proximityScore,
      clusterAssignment: locationAnalysis.clusterAssignment,
      distanceAnalysis: locationAnalysis.analysis,
      confidence: this.calculateGeospatialConfidence(locationAnalysis)
    };
  }

  /**
   * Analyze temporal factors
   * @param {Object} trail - Trail object
   * @returns {Object} - Temporal analysis
   */
  analyzeTemporalFactors(trail) {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();
    
    // Seasonal scoring (June-August preferred)
    let seasonalScore = 50;
    if (currentMonth >= 6 && currentMonth <= 8) {
      seasonalScore = 90;
    } else if (currentMonth >= 5 && currentMonth <= 9) {
      seasonalScore = 70;
    }
    
    // Accessibility scoring based on elevation and season
    let accessibilityScore = 50;
    if (trail.elevation < 7000) {
      accessibilityScore = 80; // Lower elevation, more accessible year-round
    } else if (trail.elevation < 8000 && currentMonth >= 6 && currentMonth <= 9) {
      accessibilityScore = 70; // Higher elevation, accessible in summer
    }
    
    // Historical timing patterns
    const historicalPattern = this.analyzeHistoricalTiming();
    
    const temporalScore = (seasonalScore * 0.4) + (accessibilityScore * 0.4) + (historicalPattern * 0.2);
    
    return {
      score: temporalScore,
      seasonalScore: seasonalScore,
      accessibilityScore: accessibilityScore,
      historicalPattern: historicalPattern,
      currentSeason: this.getCurrentSeason(currentMonth),
      confidence: 0.7 // Temporal analysis has moderate confidence
    };
  }

  /**
   * Detect anomalies in trail characteristics
   * @param {Object} trail - Trail object
   * @param {Object} analyses - Component analyses
   * @returns {Object} - Anomaly detection results
   */
  detectAnomalies(trail, analyses) {
    const anomalies = [];
    let anomalyScore = 0;
    
    // Check for unusual elevation
    if (trail.elevation > 8000 || trail.elevation < 4000) {
      anomalies.push({
        type: 'elevation',
        severity: 'medium',
        description: 'Elevation outside typical range (4000-8000ft)'
      });
      anomalyScore += 20;
    }
    
    // Check for unusual hike time
    if (trail.hikeTime > 180 || trail.hikeTime < 15) {
      anomalies.push({
        type: 'accessibility',
        severity: 'high',
        description: 'Hike time outside typical range (15-180 minutes)'
      });
      anomalyScore += 30;
    }
    
    // Check for low geospatial probability
    if (analyses.geospatial.gmmProbability < 0.001) {
      anomalies.push({
        type: 'location',
        severity: 'medium',
        description: 'Location far from historical treasure sites'
      });
      anomalyScore += 15;
    }
    
    // Check for low NLP relevance
    if (analyses.nlp.trailRelevance.relevanceScore < 0.3) {
      anomalies.push({
        type: 'relevance',
        severity: 'low',
        description: 'Low treasure hunt relevance in trail description'
      });
      anomalyScore += 10;
    }
    
    return {
      score: Math.max(0, 100 - anomalyScore), // Higher score = fewer anomalies
      anomalies: anomalies,
      anomalyCount: anomalies.length,
      severity: this.calculateAnomalySeverity(anomalies)
    };
  }

  /**
   * Generate recommendations based on analysis
   * @param {Object} ensemblePrediction - Ensemble prediction
   * @param {Object} analyses - Component analyses
   * @returns {Array} - Recommendations
   */
  generateRecommendations(ensemblePrediction, analyses) {
    const recommendations = [];
    
    // High confidence recommendations
    if (ensemblePrediction.confidence > this.config.confidenceThresholds.high) {
      recommendations.push({
        type: 'high_confidence',
        message: 'High confidence prediction - strong candidate for treasure location',
        priority: 'high'
      });
    }
    
    // Geospatial recommendations
    if (analyses.geospatial.proximityAnalysis.minDistance < 2) {
      recommendations.push({
        type: 'proximity',
        message: `Very close to historical treasure location (${analyses.geospatial.proximityAnalysis.minDistance.toFixed(1)}km)`,
        priority: 'high'
      });
    }
    
    // NLP recommendations
    if (analyses.nlp.trailRelevance.relevanceScore > 0.7) {
      recommendations.push({
        type: 'relevance',
        message: 'Trail description highly relevant to treasure hunting terminology',
        priority: 'medium'
      });
    }
    
    // Temporal recommendations
    if (analyses.temporal.seasonalScore > 80) {
      recommendations.push({
        type: 'timing',
        message: 'Optimal season for treasure hunting (summer months)',
        priority: 'medium'
      });
    }
    
    // Anomaly warnings
    if (analyses.anomaly.anomalies.length > 0) {
      const highSeverityAnomalies = analyses.anomaly.anomalies.filter(a => a.severity === 'high');
      if (highSeverityAnomalies.length > 0) {
        recommendations.push({
          type: 'warning',
          message: `Unusual characteristics detected: ${highSeverityAnomalies.map(a => a.description).join(', ')}`,
          priority: 'high'
        });
      }
    }
    
    return recommendations;
  }

  /**
   * Calculate base score (original algorithm)
   * @param {Object} trail - Trail object
   * @returns {number} - Base score
   */
  calculateBaseScore(trail) {
    let score = 0;
    
    // Wasatch Front (40%)
    if (trail.wasatchFront) score += 40;
    
    // Elevation range (25%)
    if (trail.elevation >= 4500 && trail.elevation <= 7000) {
      score += 25;
    } else if (trail.elevation >= 4000 && trail.elevation <= 8000) {
      score += 15;
    }
    
    // Established trail (20%)
    if (trail.establishedTrail) score += 20;
    
    // Hike time (15%)
    if (trail.hikeTime >= 30 && trail.hikeTime <= 90) {
      score += 15;
    } else if (trail.hikeTime >= 15 && trail.hikeTime <= 120) {
      score += 10;
    }
    
    return score;
  }

  /**
   * Calculate NLP-enhanced score
   * @param {Object} trail - Trail object
   * @param {Array} clues - Available clues
   * @returns {number} - NLP-enhanced score
   */
  calculateNLPEnhancedScore(trail, clues) {
    const baseScore = this.calculateBaseScore(trail);
    const trailText = `${trail.name} ${trail.location} ${trail.description || ''}`;
    const relevance = this.nlpAnalyzer.analyzeTreasureHuntRelevance(trailText);
    
    const nlpBonus = relevance.relevanceScore * 20; // Up to 20 point bonus
    return Math.min(100, baseScore + nlpBonus);
  }

  /**
   * Calculate geospatial score
   * @param {Object} trail - Trail object
   * @returns {number} - Geospatial score
   */
  calculateGeospatialScore(trail) {
    if (!trail.coordinates) return 50;
    
    const locationAnalysis = this.geospatialAnalyzer.calculateLocationProbability(trail.coordinates);
    const proximityScore = locationAnalysis.proximityScore.score * 100;
    
    return Math.min(100, proximityScore);
  }

  /**
   * Calculate temporal score
   * @param {Object} trail - Trail object
   * @returns {number} - Temporal score
   */
  calculateTemporalScore(trail) {
    const temporalAnalysis = this.analyzeTemporalFactors(trail);
    return temporalAnalysis.score;
  }

  /**
   * Calculate semantic similarity between texts
   * @param {string} text1 - First text
   * @param {string} text2 - Second text
   * @returns {number} - Similarity score (0-1)
   */
  calculateSemanticSimilarity(text1, text2) {
    const tokens1 = this.nlpAnalyzer.preprocessText(text1);
    const tokens2 = this.nlpAnalyzer.preprocessText(text2);
    
    const vector1 = this.nlpAnalyzer.calculateTFIDF(tokens1);
    const vector2 = this.nlpAnalyzer.calculateTFIDF(tokens2);
    
    return this.nlpAnalyzer.cosineSimilarity(vector1, vector2);
  }

  /**
   * Find semantic matches between trail and clues
   * @param {string} trailText - Trail description
   * @param {Array} clues - Available clues
   * @returns {Array} - Semantic matches
   */
  findSemanticMatches(trailText, clues) {
    return clues.map(clue => ({
      clueId: clue.id,
      similarity: this.calculateSemanticSimilarity(trailText, clue.content),
      matchedTerms: this.findCommonTerms(trailText, clue.content)
    })).filter(match => match.similarity > 0.1);
  }

  /**
   * Find common terms between texts
   * @param {string} text1 - First text
   * @param {string} text2 - Second text
   * @returns {Array} - Common terms
   */
  findCommonTerms(text1, text2) {
    const tokens1 = new Set(this.nlpAnalyzer.preprocessText(text1));
    const tokens2 = new Set(this.nlpAnalyzer.preprocessText(text2));
    
    return [...tokens1].filter(token => tokens2.has(token));
  }

  /**
   * Calculate geospatial confidence
   * @param {Object} locationAnalysis - Location analysis results
   * @returns {number} - Confidence score
   */
  calculateGeospatialConfidence(locationAnalysis) {
    let confidence = 0.5;
    
    if (locationAnalysis.gmmProbability > 0.001) confidence += 0.2;
    if (locationAnalysis.proximityScore.minDistance < 5) confidence += 0.2;
    if (locationAnalysis.clusterAssignment?.probability > 0.5) confidence += 0.1;
    
    return Math.min(1.0, confidence);
  }

  /**
   * Analyze historical timing patterns
   * @returns {number} - Historical pattern score
   */
  analyzeHistoricalTiming() {
    // Based on historical data: hunts typically run June-August
    const currentMonth = new Date().getMonth() + 1;
    
    if (currentMonth >= 6 && currentMonth <= 8) return 90;
    if (currentMonth >= 5 && currentMonth <= 9) return 70;
    return 40;
  }

  /**
   * Get current season
   * @param {number} month - Current month (1-12)
   * @returns {string} - Season name
   */
  getCurrentSeason(month) {
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'fall';
    return 'winter';
  }

  /**
   * Calculate anomaly severity
   * @param {Array} anomalies - Detected anomalies
   * @returns {string} - Severity level
   */
  calculateAnomalySeverity(anomalies) {
    if (anomalies.some(a => a.severity === 'high')) return 'high';
    if (anomalies.some(a => a.severity === 'medium')) return 'medium';
    if (anomalies.length > 0) return 'low';
    return 'none';
  }

  /**
   * Get confidence level description
   * @param {number} confidence - Confidence score (0-1)
   * @returns {string} - Confidence level
   */
  getConfidenceLevel(confidence) {
    if (confidence >= this.config.confidenceThresholds.high) return 'high';
    if (confidence >= this.config.confidenceThresholds.medium) return 'medium';
    if (confidence >= this.config.confidenceThresholds.low) return 'low';
    return 'very_low';
  }

  /**
   * Get model versions
   * @returns {Object} - Model version information
   */
  getModelVersions() {
    return {
      bayesianEnsemble: '1.0.0',
      nlpAnalyzer: '1.0.0',
      geospatialAnalyzer: '1.0.0',
      service: '1.0.0'
    };
  }

  /**
   * Update model performance metrics
   * @param {Array} performanceData - Performance feedback
   */
  updatePerformance(performanceData) {
    if (this.config.adaptiveLearning) {
      this.bayesianEnsemble.updateWeights(performanceData);
      
      // Update service performance metrics
      const accuracy = performanceData.reduce((sum, data) => 
        sum + (1 - Math.abs(data.predicted - data.actual) / 100), 0) / performanceData.length;
      
      this.modelPerformance = {
        accuracy: accuracy,
        confidence: this.bayesianEnsemble.getModelPerformance().reduce((sum, model) => 
          sum + model.recentAccuracy, 0) / this.bayesianEnsemble.models.length,
        lastUpdated: new Date().toISOString()
      };
    }
  }

  /**
   * Get service status and performance
   * @returns {Object} - Service status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      modelPerformance: this.modelPerformance,
      config: this.config,
      modelStatus: {
        bayesianEnsemble: this.bayesianEnsemble.getModelPerformance(),
        nlpAnalyzer: !!this.nlpAnalyzer.vocabulary.size,
        geospatialAnalyzer: !!this.geospatialAnalyzer.gmmModel
      }
    };
  }

  /**
   * Export all models for persistence
   * @returns {Object} - Exportable model data
   */
  exportModels() {
    return {
      bayesianEnsemble: this.bayesianEnsemble.exportConfiguration(),
      nlpAnalyzer: this.nlpAnalyzer.exportModel(),
      geospatialAnalyzer: this.geospatialAnalyzer.exportModel(),
      config: this.config,
      performance: this.modelPerformance,
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * Import previously exported models
   * @param {Object} modelData - Exported model data
   */
  importModels(modelData) {
    if (modelData.bayesianEnsemble) {
      this.bayesianEnsemble.importConfiguration(modelData.bayesianEnsemble);
    }
    if (modelData.nlpAnalyzer) {
      this.nlpAnalyzer.importModel(modelData.nlpAnalyzer);
    }
    if (modelData.geospatialAnalyzer) {
      this.geospatialAnalyzer.importModel(modelData.geospatialAnalyzer);
    }
    if (modelData.config) {
      this.config = { ...this.config, ...modelData.config };
    }
    if (modelData.performance) {
      this.modelPerformance = modelData.performance;
    }
    
    this.isInitialized = true;
  }
}

export default AdvancedMLService;
