// Bayesian Ensemble Scoring Algorithm
// Combines multiple scoring methods with uncertainty quantification

class BayesianEnsemble {
  constructor() {
    this.models = [];
    this.priorWeights = [];
    this.posteriorWeights = [];
    this.performanceHistory = [];
  }

  /**
   * Add a scoring model to the ensemble
   * @param {Object} model - Scoring model with predict() method
   * @param {number} priorWeight - Initial belief in model quality (0-1)
   * @param {string} name - Model identifier
   */
  addModel(model, priorWeight = 0.25, name = '') {
    this.models.push({
      model: model,
      name: name || `Model_${this.models.length}`,
      predict: model.predict || model.calculateScore
    });
    this.priorWeights.push(priorWeight);
    this.posteriorWeights.push(priorWeight);
    this.performanceHistory.push([]);
  }

  /**
   * Calculate ensemble prediction with uncertainty quantification
   * @param {Object} trail - Trail object to score
   * @param {Array} clues - Available clues
   * @returns {Object} - Prediction with confidence intervals
   */
  predict(trail, clues = []) {
    const predictions = [];
    const weights = this.posteriorWeights;

    // Get predictions from all models
    for (let i = 0; i < this.models.length; i++) {
      try {
        const prediction = this.models[i].predict(trail, clues);
        const score = typeof prediction === 'object' ? prediction.total || prediction.score : prediction;
        predictions.push({
          modelIndex: i,
          score: score,
          weight: weights[i],
          modelName: this.models[i].name
        });
      } catch (error) {
        console.warn(`Model ${this.models[i].name} failed:`, error);
        predictions.push({
          modelIndex: i,
          score: 0,
          weight: 0,
          modelName: this.models[i].name
        });
      }
    }

    // Calculate weighted ensemble prediction
    const totalWeight = predictions.reduce((sum, p) => sum + p.weight, 0);
    const weightedSum = predictions.reduce((sum, p) => sum + (p.score * p.weight), 0);
    const ensembleScore = totalWeight > 0 ? weightedSum / totalWeight : 0;

    // Calculate uncertainty metrics
    const variance = this.calculateVariance(predictions, ensembleScore);
    const confidence = this.calculateConfidence(predictions, variance);
    const credibleInterval = this.calculateCredibleInterval(predictions, ensembleScore, variance);

    return {
      score: ensembleScore,
      confidence: confidence,
      uncertainty: Math.sqrt(variance),
      credibleInterval: credibleInterval,
      modelContributions: predictions.map(p => ({
        model: p.modelName,
        score: p.score,
        weight: p.weight,
        contribution: (p.score * p.weight) / totalWeight
      })),
      metadata: {
        totalWeight: totalWeight,
        activeModels: predictions.filter(p => p.weight > 0).length,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * Update model weights based on performance feedback
   * @param {Array} actualOutcomes - Array of {trailId, actualScore, predictedScore}
   */
  updateWeights(actualOutcomes) {
    if (actualOutcomes.length === 0) return;

    // Calculate model performance metrics
    const modelErrors = this.models.map(() => []);
    
    actualOutcomes.forEach(outcome => {
      const predictions = this.getPredictionsForTrail(outcome.trailId);
      predictions.forEach((pred, modelIndex) => {
        const error = Math.abs(pred.score - outcome.actualScore);
        modelErrors[modelIndex].push(error);
      });
    });

    // Update posterior weights using Bayesian updating
    for (let i = 0; i < this.models.length; i++) {
      if (modelErrors[i].length > 0) {
        const meanError = modelErrors[i].reduce((a, b) => a + b, 0) / modelErrors[i].length;
        const accuracy = Math.max(0.01, 1 - (meanError / 100)); // Convert error to accuracy
        
        // Bayesian update: P(model|data) ∝ P(data|model) × P(model)
        const likelihood = this.calculateLikelihood(accuracy);
        const prior = this.priorWeights[i];
        const posterior = likelihood * prior;
        
        this.posteriorWeights[i] = posterior;
        this.performanceHistory[i].push({
          accuracy: accuracy,
          meanError: meanError,
          sampleSize: modelErrors[i].length,
          timestamp: new Date().toISOString()
        });
      }
    }

    // Normalize weights
    this.normalizeWeights();
  }

  /**
   * Calculate likelihood of data given model performance
   * @param {number} accuracy - Model accuracy (0-1)
   * @returns {number} - Likelihood value
   */
  calculateLikelihood(accuracy) {
    // Use Beta distribution likelihood: P(accuracy|α,β)
    const alpha = 2; // Prior belief parameters
    const beta = 2;
    
    // Beta distribution PDF approximation
    return Math.pow(accuracy, alpha - 1) * Math.pow(1 - accuracy, beta - 1);
  }

  /**
   * Calculate prediction variance across models
   * @param {Array} predictions - Model predictions
   * @param {number} ensembleScore - Weighted average score
   * @returns {number} - Variance
   */
  calculateVariance(predictions, ensembleScore) {
    if (predictions.length <= 1) return 0;

    const totalWeight = predictions.reduce((sum, p) => sum + p.weight, 0);
    if (totalWeight === 0) return 0;

    const weightedVariance = predictions.reduce((sum, p) => {
      const deviation = p.score - ensembleScore;
      return sum + (p.weight * deviation * deviation);
    }, 0);

    return weightedVariance / totalWeight;
  }

  /**
   * Calculate confidence based on model agreement and uncertainty
   * @param {Array} predictions - Model predictions
   * @param {number} variance - Prediction variance
   * @returns {number} - Confidence score (0-1)
   */
  calculateConfidence(predictions, variance) {
    // Confidence decreases with variance and increases with model agreement
    const maxVariance = 2500; // Maximum expected variance (50^2)
    const varianceConfidence = Math.max(0, 1 - (variance / maxVariance));
    
    // Agreement confidence based on coefficient of variation
    const scores = predictions.map(p => p.score);
    const mean = scores.reduce((a, b) => a + b, 0) / scores.length;
    const cv = mean > 0 ? Math.sqrt(variance) / mean : 1;
    const agreementConfidence = Math.max(0, 1 - cv);
    
    // Weight confidence based on total model weight
    const totalWeight = predictions.reduce((sum, p) => sum + p.weight, 0);
    const maxWeight = predictions.length;
    const weightConfidence = totalWeight / maxWeight;
    
    // Combine confidence measures
    return (varianceConfidence * 0.4 + agreementConfidence * 0.4 + weightConfidence * 0.2);
  }

  /**
   * Calculate credible interval for prediction
   * @param {Array} predictions - Model predictions
   * @param {number} ensembleScore - Mean prediction
   * @param {number} variance - Prediction variance
   * @returns {Object} - {lower, upper} bounds
   */
  calculateCredibleInterval(predictions, ensembleScore, variance) {
    const standardError = Math.sqrt(variance);
    const confidenceLevel = 0.95; // 95% credible interval
    const zScore = 1.96; // For 95% confidence
    
    return {
      lower: Math.max(0, ensembleScore - (zScore * standardError)),
      upper: Math.min(100, ensembleScore + (zScore * standardError)),
      width: 2 * zScore * standardError
    };
  }

  /**
   * Normalize posterior weights to sum to 1
   */
  normalizeWeights() {
    const sum = this.posteriorWeights.reduce((a, b) => a + b, 0);
    if (sum > 0) {
      this.posteriorWeights = this.posteriorWeights.map(w => w / sum);
    }
  }

  /**
   * Get model performance statistics
   * @returns {Object} - Performance metrics for each model
   */
  getModelPerformance() {
    return this.models.map((model, index) => {
      const history = this.performanceHistory[index];
      const recentHistory = history.slice(-10); // Last 10 predictions
      
      return {
        name: model.name,
        priorWeight: this.priorWeights[index],
        posteriorWeight: this.posteriorWeights[index],
        totalPredictions: history.length,
        recentAccuracy: recentHistory.length > 0 
          ? recentHistory.reduce((sum, h) => sum + h.accuracy, 0) / recentHistory.length 
          : 0,
        overallAccuracy: history.length > 0 
          ? history.reduce((sum, h) => sum + h.accuracy, 0) / history.length 
          : 0,
        trend: this.calculateTrend(history)
      };
    });
  }

  /**
   * Calculate performance trend for a model
   * @param {Array} history - Performance history
   * @returns {string} - 'improving', 'declining', or 'stable'
   */
  calculateTrend(history) {
    if (history.length < 3) return 'insufficient_data';
    
    const recent = history.slice(-5);
    const older = history.slice(-10, -5);
    
    if (recent.length === 0 || older.length === 0) return 'insufficient_data';
    
    const recentAvg = recent.reduce((sum, h) => sum + h.accuracy, 0) / recent.length;
    const olderAvg = older.reduce((sum, h) => sum + h.accuracy, 0) / older.length;
    
    const difference = recentAvg - olderAvg;
    
    if (difference > 0.05) return 'improving';
    if (difference < -0.05) return 'declining';
    return 'stable';
  }

  /**
   * Export ensemble configuration
   * @returns {Object} - Serializable ensemble state
   */
  exportConfiguration() {
    return {
      modelNames: this.models.map(m => m.name),
      priorWeights: this.priorWeights,
      posteriorWeights: this.posteriorWeights,
      performanceHistory: this.performanceHistory,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Import ensemble configuration
   * @param {Object} config - Previously exported configuration
   */
  importConfiguration(config) {
    this.priorWeights = config.priorWeights || [];
    this.posteriorWeights = config.posteriorWeights || [];
    this.performanceHistory = config.performanceHistory || [];
  }
}

export default BayesianEnsemble;
