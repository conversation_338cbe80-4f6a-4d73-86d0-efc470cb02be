// Historical treasure hunt data from our research
export const historicalTreasures = [
  {
    year: 2020,
    winner: "Cameron Brunt",
    location: "Rocky Mouth Canyon",
    coordinates: [40.6089, -111.7910], // Approximate
    prizeValue: 10000, // Estimated
    duration: "Unknown",
    elevation: 5500,
    trailType: "Canyon/Mountain",
    hikeTime: 60, // minutes
    characteristics: {
      wasatchFront: true,
      establishedTrail: true,
      publicLand: true,
      moderateAccess: true
    }
  },
  {
    year: 2021,
    winner: "Andy Swanger",
    location: "Heughs Canyon Trail",
    coordinates: [40.6847, -111.8398], // Approximate
    prizeValue: 10000,
    duration: "~6 weeks",
    elevation: 5000,
    trailType: "Canyon/Foothills",
    hikeTime: 45,
    characteristics: {
      wasatchFront: true,
      establishedTrail: true,
      publicLand: true,
      moderateAccess: true
    }
  },
  {
    year: 2022,
    winner: "Family (undisclosed)",
    location: "Ben Lomond Peak area",
    coordinates: [41.3847, -111.8398], // Approximate
    prizeValue: 20000,
    duration: "Unknown",
    elevation: 6500,
    trailType: "Mountain/Peak",
    hikeTime: 90,
    characteristics: {
      wasatchFront: true,
      establishedTrail: true,
      publicLand: true,
      moderateAccess: true
    }
  },
  {
    year: 2023,
    winner: "Chelsea Gotta",
    location: "Mueller Park Trail",
    coordinates: [40.8847, -111.8398], // Approximate
    prizeValue: 25000,
    duration: "51 days",
    elevation: 5400,
    trailType: "Mountain/Forest",
    hikeTime: 75,
    characteristics: {
      wasatchFront: true,
      establishedTrail: true,
      publicLand: true,
      moderateAccess: true
    }
  }
];

export const treasureHuntRules = {
  hikeTimeRange: [0, 90], // minutes
  elevationRange: [4500, 7000], // feet
  requiredCharacteristics: [
    'wasatchFront',
    'establishedTrail',
    'publicLand',
    'moderateAccess'
  ],
  safetyRequirements: [
    'No special equipment needed',
    'Reasonable cell coverage',
    'Emergency access available'
  ]
};
