const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.txt', '.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'];
    const fileExt = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(fileExt)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only text, PDF, Word documents, and images are allowed.'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Utah Treasure Finder API is running' });
});

// Upload clue file
app.post('/api/upload-clue', upload.single('clueFile'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const filePath = req.file.path;
    const fileExt = path.extname(req.file.originalname).toLowerCase();
    
    let content = '';
    
    if (fileExt === '.txt') {
      content = fs.readFileSync(filePath, 'utf8');
    } else {
      // For other file types, return file info for client-side processing
      content = `File uploaded: ${req.file.originalname}`;
    }

    // Simple keyword extraction
    const keywords = extractKeywords(content);
    
    res.json({
      success: true,
      filename: req.file.originalname,
      content: content,
      keywords: keywords,
      fileType: fileExt,
      uploadedAt: new Date().toISOString()
    });

    // Clean up uploaded file after processing
    setTimeout(() => {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }, 5000);

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Failed to process uploaded file' });
  }
});

// Analyze clue text
app.post('/api/analyze-clue', (req, res) => {
  try {
    const { content } = req.body;
    
    if (!content) {
      return res.status(400).json({ error: 'No content provided' });
    }

    const analysis = {
      keywords: extractKeywords(content),
      wordCount: content.split(/\s+/).length,
      hasCoordinates: /\d+\.\d+/.test(content),
      hasDirections: /\b(north|south|east|west|left|right|up|down)\b/i.test(content),
      hasNumbers: /\d+/.test(content),
      hasLocationWords: /\b(mountain|peak|canyon|trail|hike|elevation|water|river|lake|stream|falls|rock|stone|cliff|ridge|valley|tree|forest|view|overlook)\b/i.test(content),
      sentiment: analyzeSentiment(content)
    };

    res.json({
      success: true,
      analysis: analysis,
      analyzedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze clue content' });
  }
});

// Get trail recommendations based on clues
app.post('/api/trail-recommendations', (req, res) => {
  try {
    const { clues, trails } = req.body;
    
    // Simple scoring based on keyword matches
    const scoredTrails = trails.map(trail => {
      let clueScore = 0;
      let matchedClues = [];

      clues.forEach(clue => {
        const matches = findKeywordMatches(trail, clue.keywords || []);
        if (matches.length > 0) {
          clueScore += matches.length * (clue.confidence || 5);
          matchedClues.push({
            clueId: clue.id,
            matches: matches,
            bonus: matches.length * (clue.confidence || 5)
          });
        }
      });

      return {
        ...trail,
        clueScore: Math.min(clueScore, 50), // Cap at 50 points
        matchedClues: matchedClues,
        updatedScore: Math.min((trail.baseScore || 0) + clueScore, 100)
      };
    });

    // Sort by updated score
    scoredTrails.sort((a, b) => b.updatedScore - a.updatedScore);

    res.json({
      success: true,
      trails: scoredTrails,
      updatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Recommendation error:', error);
    res.status(500).json({ error: 'Failed to generate trail recommendations' });
  }
});

// Helper functions

function extractKeywords(text) {
  const commonKeywords = [
    'mountain', 'peak', 'canyon', 'trail', 'hike', 'elevation',
    'north', 'south', 'east', 'west', 'up', 'down',
    'water', 'river', 'lake', 'stream', 'falls', 'spring',
    'rock', 'stone', 'cliff', 'ridge', 'valley', 'cave',
    'tree', 'forest', 'pine', 'oak', 'aspen', 'cedar',
    'view', 'overlook', 'vista', 'panorama', 'lookout',
    'church', 'cathedral', 'temple', 'historic', 'monument',
    'gold', 'treasure', 'hidden', 'buried', 'chest',
    'goonies', 'adventure', 'quest', 'search', 'find',
    'wasatch', 'salt lake', 'utah', 'ogden', 'provo',
    'bountiful', 'millcreek', 'cottonwood', 'park city'
  ];

  const words = text.toLowerCase().match(/\b\w+\b/g) || [];
  const foundKeywords = words.filter(word => 
    commonKeywords.includes(word) && word.length > 2
  );

  // Remove duplicates and return
  return [...new Set(foundKeywords)];
}

function findKeywordMatches(trail, keywords) {
  const trailText = `
    ${trail.name} 
    ${trail.location} 
    ${trail.trailType || ''} 
    ${trail.features?.join(' ') || ''} 
    ${trail.description || ''}
  `.toLowerCase();

  return keywords.filter(keyword => 
    trailText.includes(keyword.toLowerCase())
  );
}

function analyzeSentiment(text) {
  // Very simple sentiment analysis
  const positiveWords = ['beautiful', 'amazing', 'spectacular', 'gorgeous', 'stunning', 'incredible'];
  const negativeWords = ['difficult', 'dangerous', 'challenging', 'steep', 'hard', 'tough'];
  
  const words = text.toLowerCase().split(/\s+/);
  const positive = words.filter(word => positiveWords.includes(word)).length;
  const negative = words.filter(word => negativeWords.includes(word)).length;
  
  if (positive > negative) return 'positive';
  if (negative > positive) return 'negative';
  return 'neutral';
}

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large. Maximum size is 10MB.' });
    }
  }
  
  console.error('Server error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Utah Treasure Finder API server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});
