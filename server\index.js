const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.txt', '.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'];
    const fileExt = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(fileExt)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only text, PDF, Word documents, and images are allowed.'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Utah Treasure Finder API is running' });
});

// Upload clue file
app.post('/api/upload-clue', upload.single('clueFile'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const filePath = req.file.path;
    const fileExt = path.extname(req.file.originalname).toLowerCase();
    
    let content = '';
    
    if (fileExt === '.txt') {
      content = fs.readFileSync(filePath, 'utf8');
    } else {
      // For other file types, return file info for client-side processing
      content = `File uploaded: ${req.file.originalname}`;
    }

    // Simple keyword extraction
    const keywords = extractKeywords(content);
    
    res.json({
      success: true,
      filename: req.file.originalname,
      content: content,
      keywords: keywords,
      fileType: fileExt,
      uploadedAt: new Date().toISOString()
    });

    // Clean up uploaded file after processing
    setTimeout(() => {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }, 5000);

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Failed to process uploaded file' });
  }
});

// Analyze clue text
app.post('/api/analyze-clue', (req, res) => {
  try {
    const { content } = req.body;
    
    if (!content) {
      return res.status(400).json({ error: 'No content provided' });
    }

    const analysis = {
      keywords: extractKeywords(content),
      wordCount: content.split(/\s+/).length,
      hasCoordinates: /\d+\.\d+/.test(content),
      hasDirections: /\b(north|south|east|west|left|right|up|down)\b/i.test(content),
      hasNumbers: /\d+/.test(content),
      hasLocationWords: /\b(mountain|peak|canyon|trail|hike|elevation|water|river|lake|stream|falls|rock|stone|cliff|ridge|valley|tree|forest|view|overlook)\b/i.test(content),
      sentiment: analyzeSentiment(content)
    };

    res.json({
      success: true,
      analysis: analysis,
      analyzedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze clue content' });
  }
});

// Get trail recommendations based on clues
app.post('/api/trail-recommendations', (req, res) => {
  try {
    const { clues, trails } = req.body;
    
    // Simple scoring based on keyword matches
    const scoredTrails = trails.map(trail => {
      let clueScore = 0;
      let matchedClues = [];

      clues.forEach(clue => {
        const matches = findKeywordMatches(trail, clue.keywords || []);
        if (matches.length > 0) {
          clueScore += matches.length * (clue.confidence || 5);
          matchedClues.push({
            clueId: clue.id,
            matches: matches,
            bonus: matches.length * (clue.confidence || 5)
          });
        }
      });

      return {
        ...trail,
        clueScore: Math.min(clueScore, 50), // Cap at 50 points
        matchedClues: matchedClues,
        updatedScore: Math.min((trail.baseScore || 0) + clueScore, 100)
      };
    });

    // Sort by updated score
    scoredTrails.sort((a, b) => b.updatedScore - a.updatedScore);

    res.json({
      success: true,
      trails: scoredTrails,
      updatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Recommendation error:', error);
    res.status(500).json({ error: 'Failed to generate trail recommendations' });
  }
});

// Helper functions

function extractKeywords(text) {
  const commonKeywords = [
    'mountain', 'peak', 'canyon', 'trail', 'hike', 'elevation',
    'north', 'south', 'east', 'west', 'up', 'down',
    'water', 'river', 'lake', 'stream', 'falls', 'spring',
    'rock', 'stone', 'cliff', 'ridge', 'valley', 'cave',
    'tree', 'forest', 'pine', 'oak', 'aspen', 'cedar',
    'view', 'overlook', 'vista', 'panorama', 'lookout',
    'church', 'cathedral', 'temple', 'historic', 'monument',
    'gold', 'treasure', 'hidden', 'buried', 'chest',
    'goonies', 'adventure', 'quest', 'search', 'find',
    'wasatch', 'salt lake', 'utah', 'ogden', 'provo',
    'bountiful', 'millcreek', 'cottonwood', 'park city'
  ];

  const words = text.toLowerCase().match(/\b\w+\b/g) || [];
  const foundKeywords = words.filter(word => 
    commonKeywords.includes(word) && word.length > 2
  );

  // Remove duplicates and return
  return [...new Set(foundKeywords)];
}

function findKeywordMatches(trail, keywords) {
  const trailText = `
    ${trail.name} 
    ${trail.location} 
    ${trail.trailType || ''} 
    ${trail.features?.join(' ') || ''} 
    ${trail.description || ''}
  `.toLowerCase();

  return keywords.filter(keyword => 
    trailText.includes(keyword.toLowerCase())
  );
}

function analyzeSentiment(text) {
  // Very simple sentiment analysis
  const positiveWords = ['beautiful', 'amazing', 'spectacular', 'gorgeous', 'stunning', 'incredible'];
  const negativeWords = ['difficult', 'dangerous', 'challenging', 'steep', 'hard', 'tough'];
  
  const words = text.toLowerCase().split(/\s+/);
  const positive = words.filter(word => positiveWords.includes(word)).length;
  const negative = words.filter(word => negativeWords.includes(word)).length;
  
  if (positive > negative) return 'positive';
  if (negative > positive) return 'negative';
  return 'neutral';
}

// Data collection endpoints
app.post('/api/data-collection/scrape', async (req, res) => {
  try {
    const { source, respectRobots, userAgent } = req.body;

    // Simulate data scraping (in production, implement actual scraping)
    const mockData = await simulateDataScraping(source);

    res.json({
      success: true,
      source: source,
      content: mockData,
      scrapedAt: new Date().toISOString(),
      respectsRobots: respectRobots || false
    });
  } catch (error) {
    console.error('Scraping error:', error);
    res.status(500).json({ error: 'Failed to scrape data source' });
  }
});

app.get('/api/data-collection/status', (req, res) => {
  try {
    const status = {
      lastCollection: new Date().toISOString(),
      totalSources: 15,
      successfulSources: 12,
      failedSources: 3,
      dataQuality: 'Good',
      nextScheduledCollection: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      statistics: {
        riddles: 8,
        hints: 24,
        solutions: 4,
        interviews: 6,
        communityTheories: 156,
        newsArticles: 18
      }
    };

    res.json(status);
  } catch (error) {
    console.error('Status error:', error);
    res.status(500).json({ error: 'Failed to get collection status' });
  }
});

app.post('/api/data-collection/trigger', async (req, res) => {
  try {
    const { sources, timestamp } = req.body;

    // Simulate triggering data collection
    const collectionJob = {
      id: Date.now(),
      sources: sources,
      status: 'started',
      startTime: timestamp,
      estimatedCompletion: new Date(Date.now() + 30 * 60 * 1000).toISOString()
    };

    res.json({
      success: true,
      job: collectionJob,
      message: 'Data collection job started'
    });
  } catch (error) {
    console.error('Trigger error:', error);
    res.status(500).json({ error: 'Failed to trigger data collection' });
  }
});

// Pattern analysis endpoints
app.post('/api/pattern-analysis/analyze', async (req, res) => {
  try {
    const { collectedData } = req.body;

    // Simulate pattern analysis
    const analysis = await simulatePatternAnalysis(collectedData);

    res.json({
      success: true,
      analysis: analysis,
      analyzedAt: new Date().toISOString(),
      confidence: 0.85
    });
  } catch (error) {
    console.error('Pattern analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze patterns' });
  }
});

app.post('/api/enhanced-scoring/calculate', async (req, res) => {
  try {
    const { trails, patterns, clues, communityTheories } = req.body;

    // Simulate enhanced scoring
    const enhancedTrails = trails.map(trail => ({
      ...trail,
      enhancedScore: calculateMockEnhancedScore(trail, patterns),
      confidence: 0.78,
      patternMatches: ['geographic_clustering', 'organizer_preference']
    }));

    res.json({
      success: true,
      trails: enhancedTrails,
      algorithm: 'enhanced_v2.0',
      calculatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Enhanced scoring error:', error);
    res.status(500).json({ error: 'Failed to calculate enhanced scores' });
  }
});

// Helper functions for simulation
async function simulateDataScraping(source) {
  // Simulate different types of scraped content
  const mockContent = [];

  if (source.type === 'instagram') {
    mockContent.push({
      text: "New hint for the 2024 Utah Treasure Hunt! The treasure is within a 90-minute hike from parking.",
      date: "2024-07-15",
      author: "David Cline",
      source: source.url,
      type: "hint"
    });
  } else if (source.type === 'news') {
    mockContent.push({
      text: "Utah treasure hunt organizers David Cline and John Maxim explain their methodology for selecting locations.",
      date: "2024-06-20",
      author: "ABC4 News",
      source: source.urls?.[0] || source.url,
      type: "interview"
    });
  } else if (source.type === 'reddit') {
    mockContent.push({
      text: "I think the treasure might be near Ensign Peak based on the Spanish clues about 'coro' meaning choir.",
      date: "2024-07-10",
      author: "treasure_hunter_2024",
      source: source.urls?.[0] || source.url,
      type: "theory"
    });
  }

  return mockContent;
}

async function simulatePatternAnalysis(data) {
  return {
    linguisticPatterns: {
      languageUsage: { english: 75, spanish: 25 },
      keywordFrequency: { mountain: 12, peak: 8, canyon: 6, trail: 15 },
      culturalReferences: { movies: ['goonies'], religious: ['cathedral', 'choir'] }
    },
    geographicPatterns: {
      locationClustering: { wasatchFront: 100, averageDistance: 8.5 },
      elevationPreferences: { average: 5800, min: 4500, max: 7000 }
    },
    temporalPatterns: {
      seasonalTiming: { summer: 100, preferredMonths: [6, 7, 8] },
      huntDuration: { average: 45, range: [17, 51] }
    },
    organizerPreferences: {
      locationCriteria: ['accessible by car', 'established trail', 'scenic views'],
      safetyConsiderations: ['cell phone coverage', 'emergency access'],
      avoidanceFactors: ['private property', 'dangerous terrain']
    }
  };
}

function calculateMockEnhancedScore(trail, patterns) {
  // Simple mock calculation
  let baseScore = trail.totalScore || 50;
  let patternBonus = 0;

  if (trail.wasatchFront) patternBonus += 15;
  if (trail.elevation >= 4500 && trail.elevation <= 7000) patternBonus += 10;
  if (trail.hikeTime >= 30 && trail.hikeTime <= 90) patternBonus += 8;

  return Math.min(100, baseScore + patternBonus);
}

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large. Maximum size is 10MB.' });
    }
  }

  console.error('Server error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Utah Treasure Finder API server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
  console.log(`Data collection endpoints available at ${PORT}/api/data-collection/`);
});
