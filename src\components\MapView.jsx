import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-leaflet';
import { useTrails } from '../context/TrailContext';
import { historicalTreasures } from '../data/historicalTreasures';
import { getConfidenceLevel, getScoreColor } from '../utils/scoringAlgorithm';
import { Filter, MapPin, Trophy, Mountain, Clock } from 'lucide-react';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
import L from 'leaflet';
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

function MapView() {
  const { filteredTrails, filters, updateFilters } = useTrails();
  const [showHistorical, setShowHistorical] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTrail, setSelectedTrail] = useState(null);

  // Utah center coordinates
  const utahCenter = [39.3210, -111.0937];

  // Create custom icons based on score
  const createTrailIcon = (score) => {
    const color = score >= 80 ? '#dc2626' : score >= 65 ? '#ea580c' : score >= 50 ? '#ca8a04' : '#2563eb';
    return L.divIcon({
      className: 'custom-trail-marker',
      html: `<div style="background-color: ${color}; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
      iconSize: [20, 20],
      iconAnchor: [10, 10]
    });
  };

  const createHistoricalIcon = () => {
    return L.divIcon({
      className: 'custom-historical-marker',
      html: `<div style="background-color: #7c3aed; width: 24px; height: 24px; border-radius: 50%; border: 3px solid #fbbf24; box-shadow: 0 2px 6px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center;"><span style="color: white; font-size: 12px; font-weight: bold;">★</span></div>`,
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Interactive Trail Map</h1>
          <p className="text-gray-600">Explore Utah trails with treasure likelihood scoring</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn-secondary flex items-center space-x-2"
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
          </button>
          
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={showHistorical}
              onChange={(e) => setShowHistorical(e.target.checked)}
              className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
            />
            <span className="text-sm text-gray-700">Show Historical Locations</span>
          </label>
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <div className="card bg-gray-50">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Map Filters</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
              <select
                value={filters.difficulty}
                onChange={(e) => updateFilters({ difficulty: e.target.value })}
                className="input-field"
              >
                <option value="all">All Difficulties</option>
                <option value="Easy">Easy</option>
                <option value="Moderate">Moderate</option>
                <option value="Hard">Hard</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Region</label>
              <select
                value={filters.region}
                onChange={(e) => updateFilters({ region: e.target.value })}
                className="input-field"
              >
                <option value="all">All Regions</option>
                <option value="salt-lake">Salt Lake Area</option>
                <option value="utah-county">Utah County</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Min Score: {filters.scoreRange[0]}
              </label>
              <input
                type="range"
                min="0"
                max="100"
                value={filters.scoreRange[0]}
                onChange={(e) => updateFilters({ 
                  scoreRange: [parseInt(e.target.value), filters.scoreRange[1]] 
                })}
                className="w-full"
              />
            </div>
            
            <div className="flex items-center">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={filters.wasatchFrontOnly}
                  onChange={(e) => updateFilters({ wasatchFrontOnly: e.target.checked })}
                  className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
                />
                <span className="text-sm text-gray-700">Wasatch Front Only</span>
              </label>
            </div>
          </div>
        </div>
      )}

      {/* Legend */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Map Legend</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded-full border-2 border-white shadow"></div>
            <span className="text-sm text-gray-700">Very High (80+)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-orange-500 rounded-full border-2 border-white shadow"></div>
            <span className="text-sm text-gray-700">High (65-79)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-yellow-500 rounded-full border-2 border-white shadow"></div>
            <span className="text-sm text-gray-700">Medium (50-64)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow"></div>
            <span className="text-sm text-gray-700">Low (35-49)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-purple-500 rounded-full border-3 border-yellow-400 shadow flex items-center justify-center">
              <span className="text-white text-xs font-bold">★</span>
            </div>
            <span className="text-sm text-gray-700">Historical Find</span>
          </div>
        </div>
      </div>

      {/* Map Container */}
      <div className="card p-0 overflow-hidden">
        <div style={{ height: '600px', width: '100%' }}>
          <MapContainer
            center={utahCenter}
            zoom={8}
            style={{ height: '100%', width: '100%' }}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            
            {/* Trail Markers */}
            {filteredTrails.map((trail) => (
              <Marker
                key={trail.id}
                position={trail.coordinates}
                icon={createTrailIcon(trail.totalScore)}
                eventHandlers={{
                  click: () => setSelectedTrail(trail)
                }}
              >
                <Popup>
                  <div className="p-2 min-w-64">
                    <h3 className="font-bold text-lg text-gray-900 mb-2">{trail.name}</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Score:</span>
                        <span className={`px-2 py-1 rounded text-sm font-bold ${getScoreColor(trail.totalScore)}`}>
                          {trail.totalScore.toFixed(1)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Confidence:</span>
                        <span className="text-sm font-medium">{getConfidenceLevel(trail.totalScore)}</span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <Mountain className="w-4 h-4" />
                          <span>{trail.elevation}ft</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="w-4 h-4" />
                          <span>{trail.hikeTime}min</span>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700">{trail.description}</p>
                    </div>
                  </div>
                </Popup>
              </Marker>
            ))}
            
            {/* Historical Treasure Locations */}
            {showHistorical && historicalTreasures.map((treasure) => (
              <Marker
                key={treasure.year}
                position={treasure.coordinates}
                icon={createHistoricalIcon()}
              >
                <Popup>
                  <div className="p-2 min-w-48">
                    <h3 className="font-bold text-lg text-purple-900 mb-2">
                      {treasure.year} Treasure Found
                    </h3>
                    <div className="space-y-1 text-sm">
                      <p><strong>Location:</strong> {treasure.location}</p>
                      <p><strong>Winner:</strong> {treasure.winner}</p>
                      <p><strong>Prize:</strong> ${treasure.prizeValue.toLocaleString()}</p>
                      <p><strong>Elevation:</strong> {treasure.elevation}ft</p>
                      <p><strong>Hike Time:</strong> {treasure.hikeTime} minutes</p>
                    </div>
                  </div>
                </Popup>
              </Marker>
            ))}
          </MapContainer>
        </div>
      </div>

      {/* Trail Details Panel */}
      {selectedTrail && (
        <div className="card bg-gradient-to-r from-treasure-50 to-utah-50">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-bold text-gray-900">Selected Trail Details</h3>
            <button
              onClick={() => setSelectedTrail(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">{selectedTrail.name}</h4>
              <p className="text-gray-700 mb-4">{selectedTrail.description}</p>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Location:</span>
                  <span className="font-medium">{selectedTrail.location}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Difficulty:</span>
                  <span className="font-medium">{selectedTrail.difficulty}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Length:</span>
                  <span className="font-medium">{selectedTrail.length} miles</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Elevation Gain:</span>
                  <span className="font-medium">{selectedTrail.elevationGain}ft</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Treasure Likelihood Analysis</h4>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Overall Score:</span>
                  <span className={`px-3 py-1 rounded-full font-bold ${getScoreColor(selectedTrail.totalScore)}`}>
                    {selectedTrail.totalScore.toFixed(1)}
                  </span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Wasatch Front:</span>
                    <span>{selectedTrail.scoreBreakdown?.wasatchFront || 0}/100</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Elevation Range:</span>
                    <span>{selectedTrail.scoreBreakdown?.elevationRange || 0}/100</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Trail System:</span>
                    <span>{selectedTrail.scoreBreakdown?.establishedTrail || 0}/100</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Accessibility:</span>
                    <span>{selectedTrail.scoreBreakdown?.hikeTimeRange || 0}/100</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default MapView;
