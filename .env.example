# Utah Treasure Finder Environment Configuration

# Server Configuration
PORT=3001
NODE_ENV=development

# API Keys (Optional - for enhanced features)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
OPENWEATHER_API_KEY=your_openweather_api_key_here

# Database Configuration (if using external database)
DATABASE_URL=sqlite://./treasure_finder.db

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=uploads

# Security Configuration
JWT_SECRET=your_jwt_secret_here
CORS_ORIGIN=http://localhost:3000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Feature Flags
ENABLE_FILE_UPLOAD=true
ENABLE_ADVANCED_ANALYSIS=true
ENABLE_EXPORT_FEATURES=true

# External API Configuration
ALLTRAILS_API_KEY=your_alltrails_api_key_here
UTAH_TRAILS_API_KEY=your_utah_trails_api_key_here
