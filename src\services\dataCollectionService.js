// Comprehensive data collection service for Utah Treasure Finder
import axios from 'axios';

class DataCollectionService {
  constructor() {
    this.baseURL = '/api/data-collection';
    this.rateLimitDelay = 2000; // 2 seconds between requests
    this.maxRetries = 3;
  }

  /**
   * Collect all official organizer content
   */
  async collectOfficialContent() {
    const sources = [
      {
        type: 'instagram',
        url: 'https://www.instagram.com/the.cline.fam/',
        description: '<PERSON> official Instagram account'
      },
      {
        type: 'news',
        urls: [
          'https://www.abc4.com/news/utah-treasure-hunt/',
          'https://www.ksl.com/search?q=utah+treasure+hunt',
          'https://www.fox13now.com/search?q=utah+treasure+hunt',
          'https://www.deseret.com/search?q=utah+treasure+hunt'
        ],
        description: 'News media coverage'
      },
      {
        type: 'reddit',
        urls: [
          'https://www.reddit.com/r/utahtreasurehunt/',
          'https://www.reddit.com/r/TreasureHunting/search/?q=utah'
        ],
        description: 'Community discussions'
      }
    ];

    const collectedData = {
      riddles: [],
      hints: [],
      solutions: [],
      interviews: [],
      communityTheories: [],
      officialRules: []
    };

    for (const source of sources) {
      try {
        await this.delay(this.rateLimitDelay);
        const data = await this.scrapeSource(source);
        this.categorizeAndStore(data, collectedData);
      } catch (error) {
        console.error(`Failed to collect from ${source.type}:`, error);
      }
    }

    return collectedData;
  }

  /**
   * Scrape individual data source with retry logic
   */
  async scrapeSource(source, retryCount = 0) {
    try {
      const response = await axios.post(`${this.baseURL}/scrape`, {
        source: source,
        respectRobots: true,
        userAgent: 'Utah Treasure Finder Research Bot 1.0'
      });

      return response.data;
    } catch (error) {
      if (retryCount < this.maxRetries) {
        await this.delay(this.rateLimitDelay * (retryCount + 1));
        return this.scrapeSource(source, retryCount + 1);
      }
      throw error;
    }
  }

  /**
   * Categorize scraped data into appropriate buckets
   */
  categorizeAndStore(rawData, collectedData) {
    if (!rawData || !rawData.content) return;

    rawData.content.forEach(item => {
      // Categorize based on content type and keywords
      if (this.isRiddle(item.text)) {
        collectedData.riddles.push({
          ...item,
          type: 'riddle',
          year: this.extractYear(item.text || item.date),
          keywords: this.extractKeywords(item.text),
          confidence: this.calculateConfidence(item)
        });
      } else if (this.isHint(item.text)) {
        collectedData.hints.push({
          ...item,
          type: 'hint',
          year: this.extractYear(item.text || item.date),
          keywords: this.extractKeywords(item.text),
          confidence: this.calculateConfidence(item)
        });
      } else if (this.isSolution(item.text)) {
        collectedData.solutions.push({
          ...item,
          type: 'solution',
          year: this.extractYear(item.text || item.date),
          location: this.extractLocation(item.text),
          explanation: item.text
        });
      } else if (this.isInterview(item.text)) {
        collectedData.interviews.push({
          ...item,
          type: 'interview',
          year: this.extractYear(item.text || item.date),
          insights: this.extractInsights(item.text)
        });
      } else if (this.isCommunityTheory(item.text)) {
        collectedData.communityTheories.push({
          ...item,
          type: 'theory',
          year: this.extractYear(item.text || item.date),
          location: this.extractLocation(item.text),
          reasoning: item.text
        });
      }
    });
  }

  /**
   * Content classification methods
   */
  isRiddle(text) {
    if (!text) return false;
    const riddleIndicators = [
      'si sufres dolor',
      'busca el atajo',
      'where time',
      'begin your search',
      'riddle',
      'poem'
    ];
    return riddleIndicators.some(indicator => 
      text.toLowerCase().includes(indicator.toLowerCase())
    );
  }

  isHint(text) {
    if (!text) return false;
    const hintIndicators = [
      'hint',
      'clue',
      'at an average pace',
      'treasure chest is between',
      'minute hike',
      'no need to',
      'pop culture reference'
    ];
    return hintIndicators.some(indicator => 
      text.toLowerCase().includes(indicator.toLowerCase())
    );
  }

  isSolution(text) {
    if (!text) return false;
    const solutionIndicators = [
      'treasure found',
      'solution',
      'breakdown',
      'explanation',
      'winner',
      'located at',
      'hidden at'
    ];
    return solutionIndicators.some(indicator => 
      text.toLowerCase().includes(indicator.toLowerCase())
    );
  }

  isInterview(text) {
    if (!text) return false;
    const interviewIndicators = [
      'david cline',
      'john maxim',
      'organizer',
      'interview',
      'explains',
      'methodology'
    ];
    return interviewIndicators.some(indicator => 
      text.toLowerCase().includes(indicator.toLowerCase())
    );
  }

  isCommunityTheory(text) {
    if (!text) return false;
    const theoryIndicators = [
      'theory',
      'i think',
      'my guess',
      'could be',
      'probably',
      'location might'
    ];
    return theoryIndicators.some(indicator => 
      text.toLowerCase().includes(indicator.toLowerCase())
    );
  }

  /**
   * Extract year from text or date
   */
  extractYear(text) {
    if (!text) return null;
    const yearMatch = text.match(/20(20|21|22|23|24|25)/);
    return yearMatch ? parseInt(yearMatch[0]) : null;
  }

  /**
   * Extract keywords using enhanced NLP
   */
  extractKeywords(text) {
    if (!text) return [];
    
    const locationKeywords = [
      'mountain', 'peak', 'canyon', 'trail', 'hike', 'elevation',
      'wasatch', 'salt lake', 'ogden', 'provo', 'bountiful',
      'millcreek', 'cottonwood', 'park city', 'draper',
      'north', 'south', 'east', 'west', 'up', 'down',
      'water', 'river', 'lake', 'stream', 'falls',
      'rock', 'stone', 'cliff', 'ridge', 'valley',
      'tree', 'forest', 'pine', 'oak', 'aspen',
      'view', 'overlook', 'vista', 'panorama',
      'church', 'cathedral', 'temple', 'historic',
      'gold', 'oro', 'treasure', 'chest', 'hidden',
      'goonies', 'adventure', 'quest', 'search'
    ];

    const words = text.toLowerCase().match(/\b\w+\b/g) || [];
    return words.filter(word => 
      locationKeywords.includes(word) && word.length > 2
    );
  }

  /**
   * Extract location information from text
   */
  extractLocation(text) {
    if (!text) return null;
    
    const locationPatterns = [
      /([A-Z][a-z]+ (?:Peak|Mountain|Canyon|Trail|Park))/g,
      /([A-Z][a-z]+ [A-Z][a-z]+ (?:Trail|Canyon|Peak))/g,
      /(Mueller Park|Ben Lomond|Rocky Mouth|Heughs Canyon)/gi
    ];

    for (const pattern of locationPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        return matches[0];
      }
    }

    return null;
  }

  /**
   * Extract insights from interview content
   */
  extractInsights(text) {
    if (!text) return [];
    
    const insightPatterns = [
      /we (?:like to|prefer to|always|never) ([^.]+)/gi,
      /the treasure (?:is|will be|won't be) ([^.]+)/gi,
      /we (?:hide|place|put) (?:it|the treasure) ([^.]+)/gi,
      /(?:elevation|distance|time) (?:is|should be|must be) ([^.]+)/gi
    ];

    const insights = [];
    for (const pattern of insightPatterns) {
      const matches = [...text.matchAll(pattern)];
      insights.push(...matches.map(match => match[1].trim()));
    }

    return insights;
  }

  /**
   * Calculate confidence score for data item
   */
  calculateConfidence(item) {
    let confidence = 5; // Base confidence
    
    // Source reliability
    if (item.source?.includes('abc4') || item.source?.includes('ksl')) {
      confidence += 3; // News sources are reliable
    }
    if (item.source?.includes('instagram') && item.source?.includes('cline')) {
      confidence += 4; // Official organizer content
    }
    if (item.source?.includes('reddit')) {
      confidence += 1; // Community content is less reliable
    }

    // Content quality indicators
    if (item.text?.length > 100) confidence += 1;
    if (item.date) confidence += 1;
    if (item.author?.includes('cline') || item.author?.includes('maxim')) {
      confidence += 3;
    }

    return Math.min(confidence, 10);
  }

  /**
   * Delay function for rate limiting
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get collection status and statistics
   */
  async getCollectionStatus() {
    try {
      const response = await axios.get(`${this.baseURL}/status`);
      return response.data;
    } catch (error) {
      console.error('Failed to get collection status:', error);
      return null;
    }
  }

  /**
   * Trigger manual data collection
   */
  async triggerCollection(sources = 'all') {
    try {
      const response = await axios.post(`${this.baseURL}/trigger`, {
        sources: sources,
        timestamp: new Date().toISOString()
      });
      return response.data;
    } catch (error) {
      console.error('Failed to trigger collection:', error);
      throw error;
    }
  }
}

export default new DataCollectionService();
