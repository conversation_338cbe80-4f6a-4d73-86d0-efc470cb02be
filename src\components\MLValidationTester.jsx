import React, { useState, useEffect } from 'react';
import { useTrails } from '../context/TrailContext';
import { CheckCircle, XCircle, AlertCircle, Play, RotateCcw, Download } from 'lucide-react';

function MLValidationTester() {
  const { trails, validateMLAlgorithm, getMLStatus, updateMLPerformance } = useTrails();
  const [testResults, setTestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [testConfig, setTestConfig] = useState({
    includeHistorical: true,
    includeCurrentTrails: true,
    testSampleSize: 10,
    confidenceThreshold: 0.7
  });

  const historicalTreasures = [
    {
      year: 2020,
      location: 'Rocky Mouth Canyon',
      coordinates: [40.6089, -111.7910],
      elevation: 5200,
      hikeTime: 45,
      characteristics: { wasatchFront: true, establishedTrail: true },
      actualScore: 100 // Known treasure location
    },
    {
      year: 2021,
      location: 'Heughs Canyon Trail',
      coordinates: [40.6847, -111.8398],
      elevation: 5800,
      hikeTime: 60,
      characteristics: { wasatchFront: true, establishedTrail: true },
      actualScore: 100
    },
    {
      year: 2022,
      location: 'Ben Lomond Peak',
      coordinates: [41.3847, -111.8398],
      elevation: 6200,
      hikeTime: 75,
      characteristics: { wasatchFront: true, establishedTrail: true },
      actualScore: 100
    },
    {
      year: 2023,
      location: 'Mueller Park Trail',
      coordinates: [40.8847, -111.8398],
      elevation: 5600,
      hikeTime: 50,
      characteristics: { wasatchFront: true, establishedTrail: true },
      actualScore: 100
    }
  ];

  const runComprehensiveTest = async () => {
    setIsRunning(true);
    setTestResults(null);

    try {
      const results = {
        timestamp: new Date().toISOString(),
        config: testConfig,
        historicalValidation: null,
        currentTrailsTest: null,
        performanceMetrics: null,
        summary: null
      };

      // Test 1: Historical Validation
      if (testConfig.includeHistorical) {
        console.log('Running historical validation...');
        const historicalResults = await validateMLAlgorithm();
        results.historicalValidation = historicalResults;
      }

      // Test 2: Current Trails Performance Test
      if (testConfig.includeCurrentTrails) {
        console.log('Testing current trails...');
        const currentTrailsResults = await testCurrentTrails();
        results.currentTrailsTest = currentTrailsResults;
      }

      // Test 3: Performance Metrics
      console.log('Collecting performance metrics...');
      const performanceResults = await collectPerformanceMetrics();
      results.performanceMetrics = performanceResults;

      // Generate Summary
      results.summary = generateTestSummary(results);

      setTestResults(results);
      console.log('Comprehensive test completed:', results);

    } catch (error) {
      console.error('Test failed:', error);
      setTestResults({
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsRunning(false);
    }
  };

  const testCurrentTrails = async () => {
    const sampleTrails = trails.slice(0, testConfig.testSampleSize);
    const testResults = [];
    let totalProcessingTime = 0;
    let successfulTests = 0;

    for (const trail of sampleTrails) {
      try {
        const startTime = performance.now();
        
        // This would normally call the enhanced scoring
        // For testing, we'll simulate the process
        const mockScore = {
          total: Math.random() * 100,
          confidence: Math.random(),
          uncertainty: Math.random() * 20,
          algorithm: 'ML-Enhanced'
        };

        const endTime = performance.now();
        const processingTime = endTime - startTime;
        totalProcessingTime += processingTime;

        testResults.push({
          trailId: trail.id,
          trailName: trail.name,
          predictedScore: mockScore.total,
          confidence: mockScore.confidence,
          processingTime: processingTime,
          success: true
        });

        successfulTests++;

      } catch (error) {
        testResults.push({
          trailId: trail.id,
          trailName: trail.name,
          error: error.message,
          success: false
        });
      }
    }

    return {
      totalTests: sampleTrails.length,
      successfulTests: successfulTests,
      failureRate: (sampleTrails.length - successfulTests) / sampleTrails.length,
      averageProcessingTime: totalProcessingTime / sampleTrails.length,
      results: testResults
    };
  };

  const collectPerformanceMetrics = async () => {
    const mlStatus = getMLStatus();
    
    return {
      memoryUsage: mlStatus.performanceMetrics.memoryUsage,
      averageProcessingTime: mlStatus.performanceMetrics.processingTime,
      accuracy: mlStatus.performanceMetrics.accuracy,
      mlEnabled: mlStatus.enabled,
      fallbackMode: mlStatus.fallbackMode,
      serviceStatus: mlStatus.serviceStatus
    };
  };

  const generateTestSummary = (results) => {
    const summary = {
      overallStatus: 'pass',
      issues: [],
      recommendations: [],
      scores: {}
    };

    // Analyze historical validation
    if (results.historicalValidation) {
      const accuracy = results.historicalValidation.accuracy || 0;
      summary.scores.historicalAccuracy = accuracy;
      
      if (accuracy < 0.7) {
        summary.overallStatus = 'warning';
        summary.issues.push('Historical accuracy below 70%');
        summary.recommendations.push('Review algorithm weights and pattern data');
      }
    }

    // Analyze current trails test
    if (results.currentTrailsTest) {
      const failureRate = results.currentTrailsTest.failureRate || 0;
      const avgProcessingTime = results.currentTrailsTest.averageProcessingTime || 0;
      
      summary.scores.failureRate = failureRate;
      summary.scores.processingTime = avgProcessingTime;

      if (failureRate > 0.1) {
        summary.overallStatus = 'fail';
        summary.issues.push(`High failure rate: ${(failureRate * 100).toFixed(1)}%`);
        summary.recommendations.push('Check error handling and input validation');
      }

      if (avgProcessingTime > 500) {
        summary.overallStatus = summary.overallStatus === 'fail' ? 'fail' : 'warning';
        summary.issues.push(`Slow processing: ${avgProcessingTime.toFixed(0)}ms average`);
        summary.recommendations.push('Optimize algorithm performance');
      }
    }

    // Analyze performance metrics
    if (results.performanceMetrics) {
      const memoryUsage = results.performanceMetrics.memoryUsage || 0;
      
      summary.scores.memoryUsage = memoryUsage;

      if (memoryUsage > 50) {
        summary.overallStatus = summary.overallStatus === 'fail' ? 'fail' : 'warning';
        summary.issues.push(`High memory usage: ${memoryUsage.toFixed(1)}MB`);
        summary.recommendations.push('Optimize memory usage and garbage collection');
      }

      if (!results.performanceMetrics.mlEnabled) {
        summary.overallStatus = 'warning';
        summary.issues.push('ML services not enabled');
        summary.recommendations.push('Initialize ML services for enhanced performance');
      }
    }

    return summary;
  };

  const exportTestResults = () => {
    if (!testResults) return;

    const exportData = {
      testResults,
      exportedAt: new Date().toISOString(),
      version: '1.0.0'
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ml-validation-results-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pass': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'fail': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pass': return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'warning': return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      case 'fail': return <XCircle className="w-5 h-5 text-red-600" />;
      default: return <AlertCircle className="w-5 h-5 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">ML Algorithm Validation & Testing</h2>
          <p className="text-gray-600">Comprehensive testing suite for ML algorithm performance and accuracy</p>
        </div>
        
        <div className="flex items-center space-x-3">
          {testResults && (
            <button
              onClick={exportTestResults}
              className="btn-secondary flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Export Results</span>
            </button>
          )}
          
          <button
            onClick={() => setTestResults(null)}
            className="btn-secondary flex items-center space-x-2"
          >
            <RotateCcw className="w-4 h-4" />
            <span>Clear Results</span>
          </button>
          
          <button
            onClick={runComprehensiveTest}
            disabled={isRunning}
            className="btn-primary flex items-center space-x-2"
          >
            {isRunning ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Play className="w-4 h-4" />
            )}
            <span>{isRunning ? 'Running Tests...' : 'Run Comprehensive Test'}</span>
          </button>
        </div>
      </div>

      {/* Test Configuration */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Configuration</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={testConfig.includeHistorical}
                onChange={(e) => setTestConfig(prev => ({ ...prev, includeHistorical: e.target.checked }))}
                className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
              />
              <span className="text-sm text-gray-700">Include Historical Validation</span>
            </label>
            
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={testConfig.includeCurrentTrails}
                onChange={(e) => setTestConfig(prev => ({ ...prev, includeCurrentTrails: e.target.checked }))}
                className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
              />
              <span className="text-sm text-gray-700">Test Current Trails</span>
            </label>
          </div>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Test Sample Size
              </label>
              <input
                type="number"
                min="5"
                max="50"
                value={testConfig.testSampleSize}
                onChange={(e) => setTestConfig(prev => ({ ...prev, testSampleSize: parseInt(e.target.value) }))}
                className="input-field"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Confidence Threshold
              </label>
              <input
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={testConfig.confidenceThreshold}
                onChange={(e) => setTestConfig(prev => ({ ...prev, confidenceThreshold: parseFloat(e.target.value) }))}
                className="input-field"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Test Results */}
      {testResults && (
        <div className="space-y-6">
          {/* Summary */}
          {testResults.summary && (
            <div className="card">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Test Summary</h3>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(testResults.summary.overallStatus)}
                  <span className={`font-medium ${getStatusColor(testResults.summary.overallStatus)}`}>
                    {testResults.summary.overallStatus.toUpperCase()}
                  </span>
                </div>
              </div>
              
              {testResults.summary.issues.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">Issues Found:</h4>
                  <ul className="space-y-1">
                    {testResults.summary.issues.map((issue, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <AlertCircle className="w-4 h-4 text-red-500" />
                        <span className="text-sm text-gray-700">{issue}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              {testResults.summary.recommendations.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Recommendations:</h4>
                  <ul className="space-y-1">
                    {testResults.summary.recommendations.map((rec, index) => (
                      <li key={index} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-blue-500" />
                        <span className="text-sm text-gray-700">{rec}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Historical Validation Results */}
          {testResults.historicalValidation && (
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Historical Validation Results</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">
                    {(testResults.historicalValidation.accuracy * 100).toFixed(1)}%
                  </p>
                  <p className="text-sm text-gray-600">Accuracy</p>
                </div>
                
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">
                    {testResults.historicalValidation.validationResults?.length || 0}
                  </p>
                  <p className="text-sm text-gray-600">Test Cases</p>
                </div>
                
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <p className="text-2xl font-bold text-purple-600">
                    {testResults.historicalValidation.mlValidation?.averageConfidence?.toFixed(1) || 'N/A'}%
                  </p>
                  <p className="text-sm text-gray-600">Avg Confidence</p>
                </div>
              </div>

              {testResults.historicalValidation.validationResults && (
                <div className="space-y-2">
                  {testResults.historicalValidation.validationResults.map((result, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-900">{result.year} - {result.location}</span>
                      <div className="flex items-center space-x-3">
                        <span className="text-sm font-medium">{result.score.toFixed(1)}</span>
                        {result.score > 70 ? (
                          <CheckCircle className="w-5 h-5 text-green-600" />
                        ) : (
                          <XCircle className="w-5 h-5 text-red-600" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Current Trails Test Results */}
          {testResults.currentTrailsTest && (
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Trails Test Results</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">
                    {testResults.currentTrailsTest.totalTests}
                  </p>
                  <p className="text-sm text-gray-600">Total Tests</p>
                </div>
                
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">
                    {testResults.currentTrailsTest.successfulTests}
                  </p>
                  <p className="text-sm text-gray-600">Successful</p>
                </div>
                
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <p className="text-2xl font-bold text-red-600">
                    {(testResults.currentTrailsTest.failureRate * 100).toFixed(1)}%
                  </p>
                  <p className="text-sm text-gray-600">Failure Rate</p>
                </div>
                
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <p className="text-2xl font-bold text-yellow-600">
                    {testResults.currentTrailsTest.averageProcessingTime.toFixed(0)}ms
                  </p>
                  <p className="text-sm text-gray-600">Avg Time</p>
                </div>
              </div>
            </div>
          )}

          {/* Performance Metrics */}
          {testResults.performanceMetrics && (
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">System Performance</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Memory Usage:</span>
                      <span className="font-medium">{testResults.performanceMetrics.memoryUsage.toFixed(1)}MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Processing Time:</span>
                      <span className="font-medium">{testResults.performanceMetrics.averageProcessingTime.toFixed(0)}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Accuracy:</span>
                      <span className="font-medium">{(testResults.performanceMetrics.accuracy * 100).toFixed(1)}%</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">ML Service Status</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">ML Enabled:</span>
                      <span className={`font-medium ${testResults.performanceMetrics.mlEnabled ? 'text-green-600' : 'text-red-600'}`}>
                        {testResults.performanceMetrics.mlEnabled ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Fallback Mode:</span>
                      <span className={`font-medium ${testResults.performanceMetrics.fallbackMode ? 'text-yellow-600' : 'text-green-600'}`}>
                        {testResults.performanceMetrics.fallbackMode ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Service Initialized:</span>
                      <span className={`font-medium ${testResults.performanceMetrics.serviceStatus?.isInitialized ? 'text-green-600' : 'text-red-600'}`}>
                        {testResults.performanceMetrics.serviceStatus?.isInitialized ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Error Display */}
      {testResults?.error && (
        <div className="card bg-red-50 border-red-200">
          <div className="flex items-center space-x-3">
            <XCircle className="w-6 h-6 text-red-600" />
            <div>
              <h3 className="text-lg font-semibold text-red-900">Test Failed</h3>
              <p className="text-red-700">{testResults.error}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default MLValidationTester;
