// Map utility functions for the Utah Treasure Finder application

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param {number} lat1 - Latitude of first point
 * @param {number} lon1 - Longitude of first point
 * @param {number} lat2 - Latitude of second point
 * @param {number} lon2 - Longitude of second point
 * @returns {number} Distance in miles
 */
export function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 3959; // Earth's radius in miles
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * Convert degrees to radians
 * @param {number} degrees - Degrees to convert
 * @returns {number} Radians
 */
function toRadians(degrees) {
  return degrees * (Math.PI / 180);
}

/**
 * Get map bounds for Utah state
 * @returns {Array} Bounds array [[south, west], [north, east]]
 */
export function getUtahBounds() {
  return [
    [36.9979, -114.0529], // Southwest corner
    [42.0013, -109.0489]  // Northeast corner
  ];
}

/**
 * Get Wasatch Front region bounds
 * @returns {Array} Bounds array [[south, west], [north, east]]
 */
export function getWasatchFrontBounds() {
  return [
    [39.5, -112.1], // Southwest
    [41.8, -111.4]  // Northeast
  ];
}

/**
 * Check if coordinates are within Wasatch Front region
 * @param {number} lat - Latitude
 * @param {number} lon - Longitude
 * @returns {boolean} True if within Wasatch Front
 */
export function isInWasatchFront(lat, lon) {
  const bounds = getWasatchFrontBounds();
  return lat >= bounds[0][0] && lat <= bounds[1][0] &&
         lon >= bounds[0][1] && lon <= bounds[1][1];
}

/**
 * Get center point of Utah
 * @returns {Array} [latitude, longitude]
 */
export function getUtahCenter() {
  return [39.3210, -111.0937];
}

/**
 * Get optimal zoom level based on trail density
 * @param {Array} trails - Array of trail objects
 * @returns {number} Zoom level
 */
export function getOptimalZoom(trails) {
  if (trails.length === 0) return 8;
  
  // Calculate bounds of all trails
  const lats = trails.map(t => t.coordinates[0]);
  const lons = trails.map(t => t.coordinates[1]);
  
  const latRange = Math.max(...lats) - Math.min(...lats);
  const lonRange = Math.max(...lons) - Math.min(...lons);
  
  const maxRange = Math.max(latRange, lonRange);
  
  if (maxRange > 3) return 7;
  if (maxRange > 1.5) return 8;
  if (maxRange > 0.5) return 9;
  return 10;
}

/**
 * Create custom marker icon based on trail score
 * @param {number} score - Trail score (0-100)
 * @param {string} type - Marker type ('trail' or 'historical')
 * @returns {Object} Leaflet icon configuration
 */
export function createCustomIcon(score, type = 'trail') {
  if (type === 'historical') {
    return {
      className: 'custom-historical-marker',
      html: `<div style="background-color: #7c3aed; width: 24px; height: 24px; border-radius: 50%; border: 3px solid #fbbf24; box-shadow: 0 2px 6px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center;"><span style="color: white; font-size: 12px; font-weight: bold;">★</span></div>`,
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    };
  }
  
  const color = getMarkerColor(score);
  return {
    className: 'custom-trail-marker',
    html: `<div style="background-color: ${color}; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
    iconSize: [20, 20],
    iconAnchor: [10, 10]
  };
}

/**
 * Get marker color based on score
 * @param {number} score - Trail score (0-100)
 * @returns {string} Hex color code
 */
function getMarkerColor(score) {
  if (score >= 80) return '#dc2626'; // Red
  if (score >= 65) return '#ea580c'; // Orange
  if (score >= 50) return '#ca8a04'; // Yellow
  if (score >= 35) return '#2563eb'; // Blue
  return '#6b7280'; // Gray
}

/**
 * Format coordinates for display
 * @param {Array} coordinates - [latitude, longitude]
 * @returns {string} Formatted coordinate string
 */
export function formatCoordinates(coordinates) {
  const [lat, lon] = coordinates;
  return `${lat.toFixed(4)}°, ${lon.toFixed(4)}°`;
}

/**
 * Calculate elevation gain per mile
 * @param {number} elevationGain - Total elevation gain in feet
 * @param {number} distance - Trail distance in miles
 * @returns {number} Elevation gain per mile
 */
export function calculateElevationGainPerMile(elevationGain, distance) {
  return distance > 0 ? Math.round(elevationGain / distance) : 0;
}

/**
 * Estimate hiking time based on distance and elevation gain
 * @param {number} distance - Trail distance in miles
 * @param {number} elevationGain - Elevation gain in feet
 * @returns {number} Estimated time in minutes
 */
export function estimateHikingTime(distance, elevationGain) {
  // Base time: 20 minutes per mile
  const baseTime = distance * 20;
  
  // Add time for elevation: 10 minutes per 1000 feet
  const elevationTime = (elevationGain / 1000) * 10;
  
  return Math.round(baseTime + elevationTime);
}

/**
 * Get trail difficulty based on distance and elevation gain
 * @param {number} distance - Trail distance in miles
 * @param {number} elevationGain - Elevation gain in feet
 * @returns {string} Difficulty level
 */
export function calculateTrailDifficulty(distance, elevationGain) {
  const gainPerMile = calculateElevationGainPerMile(elevationGain, distance);
  
  if (distance <= 3 && gainPerMile <= 500) return 'Easy';
  if (distance <= 6 && gainPerMile <= 1000) return 'Moderate';
  return 'Hard';
}
