import React, { useState, useEffect } from 'react';
import { useTrails } from '../context/TrailContext';
import { historicalTreasures } from '../data/historicalTreasures';
import enhancedScoringAlgorithm from '../utils/enhancedScoringAlgorithm';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { CheckCircle, XCircle, TrendingUp, Target, Zap, AlertTriangle } from 'lucide-react';

function AlgorithmValidation() {
  const { trails, patternData, useEnhancedScoring } = useTrails();
  const [validationResults, setValidationResults] = useState(null);
  const [isValidating, setIsValidating] = useState(false);
  const [comparisonData, setComparisonData] = useState([]);

  useEffect(() => {
    if (trails.length > 0) {
      runValidation();
    }
  }, [trails, useEnhancedScoring, patternData]);

  const runValidation = async () => {
    setIsValidating(true);
    try {
      const results = await validateAlgorithmAccuracy();
      setValidationResults(results);
      generateComparisonData(results);
    } catch (error) {
      console.error('Validation failed:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const validateAlgorithmAccuracy = async () => {
    const results = {
      baseAlgorithm: { accuracy: 0, scores: [], predictions: [] },
      enhancedAlgorithm: { accuracy: 0, scores: [], predictions: [] },
      improvements: {},
      recommendations: []
    };

    // Test against historical treasure locations
    for (const treasure of historicalTreasures) {
      // Create mock trail from treasure data
      const mockTrail = {
        id: `historical_${treasure.year}`,
        name: treasure.location,
        coordinates: treasure.coordinates,
        elevation: treasure.elevation,
        hikeTime: treasure.hikeTime,
        wasatchFront: treasure.characteristics.wasatchFront,
        establishedTrail: treasure.characteristics.establishedTrail,
        publicLand: treasure.characteristics.publicLand,
        trailType: treasure.trailType,
        difficulty: 'Moderate',
        length: treasure.hikeTime / 30, // Estimate based on hike time
        features: ['Historical treasure location']
      };

      // Test base algorithm
      const baseScore = calculateBaseScore(mockTrail);
      results.baseAlgorithm.scores.push({
        year: treasure.year,
        location: treasure.location,
        score: baseScore,
        actual: true
      });

      // Test enhanced algorithm if available
      if (patternData) {
        enhancedScoringAlgorithm.loadPatterns(patternData);
        const enhancedScore = enhancedScoringAlgorithm.calculateEnhancedScore(mockTrail, [], []);
        results.enhancedAlgorithm.scores.push({
          year: treasure.year,
          location: treasure.location,
          score: enhancedScore.total,
          confidence: enhancedScore.confidence,
          actual: true
        });
      }
    }

    // Test against current trail database
    trails.slice(0, 10).forEach(trail => {
      const baseScore = calculateBaseScore(trail);
      results.baseAlgorithm.scores.push({
        name: trail.name,
        score: baseScore,
        actual: false
      });

      if (patternData) {
        const enhancedScore = enhancedScoringAlgorithm.calculateEnhancedScore(trail, [], []);
        results.enhancedAlgorithm.scores.push({
          name: trail.name,
          score: enhancedScore.total,
          confidence: enhancedScore.confidence,
          actual: false
        });
      }
    });

    // Calculate accuracy metrics
    results.baseAlgorithm.accuracy = calculateAccuracy(results.baseAlgorithm.scores);
    results.enhancedAlgorithm.accuracy = calculateAccuracy(results.enhancedAlgorithm.scores);

    // Generate improvements analysis
    results.improvements = {
      accuracyImprovement: results.enhancedAlgorithm.accuracy - results.baseAlgorithm.accuracy,
      confidenceIncrease: calculateAverageConfidence(results.enhancedAlgorithm.scores),
      patternFactors: patternData ? Object.keys(patternData).length : 0
    };

    // Generate recommendations
    results.recommendations = generateRecommendations(results);

    return results;
  };

  const calculateBaseScore = (trail) => {
    // Simplified base scoring for validation
    let score = 0;
    if (trail.wasatchFront) score += 40;
    if (trail.elevation >= 4500 && trail.elevation <= 7000) score += 25;
    if (trail.establishedTrail) score += 20;
    if (trail.hikeTime >= 30 && trail.hikeTime <= 90) score += 15;
    return score;
  };

  const calculateAccuracy = (scores) => {
    const historicalScores = scores.filter(s => s.actual);
    const highScores = historicalScores.filter(s => s.score > 70);
    return historicalScores.length > 0 ? (highScores.length / historicalScores.length) * 100 : 0;
  };

  const calculateAverageConfidence = (scores) => {
    const confidenceScores = scores.filter(s => s.confidence).map(s => s.confidence);
    return confidenceScores.length > 0 
      ? (confidenceScores.reduce((a, b) => a + b, 0) / confidenceScores.length) * 100 
      : 0;
  };

  const generateRecommendations = (results) => {
    const recommendations = [];
    
    if (results.baseAlgorithm.accuracy < 80) {
      recommendations.push({
        type: 'warning',
        message: 'Base algorithm accuracy is below 80%. Consider adjusting weight factors.'
      });
    }

    if (results.improvements.accuracyImprovement > 10) {
      recommendations.push({
        type: 'success',
        message: 'Enhanced algorithm shows significant improvement. Recommend using enhanced scoring.'
      });
    }

    if (!patternData) {
      recommendations.push({
        type: 'info',
        message: 'Pattern data not loaded. Run data collection to enable enhanced scoring.'
      });
    }

    return recommendations;
  };

  const generateComparisonData = (results) => {
    const comparison = historicalTreasures.map(treasure => {
      const baseScore = results.baseAlgorithm.scores.find(s => s.year === treasure.year);
      const enhancedScore = results.enhancedAlgorithm.scores.find(s => s.year === treasure.year);
      
      return {
        year: treasure.year,
        location: treasure.location.substring(0, 15) + '...',
        baseScore: baseScore?.score || 0,
        enhancedScore: enhancedScore?.score || 0,
        confidence: enhancedScore?.confidence || 0
      };
    });
    
    setComparisonData(comparison);
  };

  if (!validationResults) {
    return (
      <div className="card">
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-treasure-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Running algorithm validation...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Algorithm Validation & Testing</h2>
          <p className="text-gray-600">Performance analysis against historical treasure hunt data</p>
        </div>
        
        <button
          onClick={runValidation}
          disabled={isValidating}
          className="btn-primary flex items-center space-x-2"
        >
          {isValidating ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          ) : (
            <Target className="w-4 h-4" />
          )}
          <span>Re-run Validation</span>
        </button>
      </div>

      {/* Accuracy Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-8 h-8 text-green-600" />
            <div>
              <p className="text-sm text-gray-600">Base Algorithm</p>
              <p className="text-2xl font-bold text-gray-900">
                {validationResults.baseAlgorithm.accuracy.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="flex items-center space-x-3">
            <Zap className="w-8 h-8 text-blue-600" />
            <div>
              <p className="text-sm text-gray-600">Enhanced Algorithm</p>
              <p className="text-2xl font-bold text-gray-900">
                {validationResults.enhancedAlgorithm.accuracy.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="flex items-center space-x-3">
            <TrendingUp className="w-8 h-8 text-treasure-600" />
            <div>
              <p className="text-sm text-gray-600">Improvement</p>
              <p className="text-2xl font-bold text-gray-900">
                +{validationResults.improvements.accuracyImprovement.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Algorithm Comparison Chart */}
      <div className="card">
        <h3 className="text-xl font-bold text-gray-900 mb-6">Historical Location Scoring Comparison</h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={comparisonData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="location" angle={-45} textAnchor="end" height={80} />
            <YAxis />
            <Tooltip />
            <Bar dataKey="baseScore" fill="#3b82f6" name="Base Algorithm" />
            <Bar dataKey="enhancedScore" fill="#eab308" name="Enhanced Algorithm" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Recommendations */}
      <div className="card">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Recommendations</h3>
        <div className="space-y-3">
          {validationResults.recommendations.map((rec, index) => (
            <div
              key={index}
              className={`flex items-start space-x-3 p-3 rounded-lg ${
                rec.type === 'success' ? 'bg-green-50 border border-green-200' :
                rec.type === 'warning' ? 'bg-yellow-50 border border-yellow-200' :
                'bg-blue-50 border border-blue-200'
              }`}
            >
              {rec.type === 'success' && <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />}
              {rec.type === 'warning' && <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />}
              {rec.type === 'info' && <TrendingUp className="w-5 h-5 text-blue-600 mt-0.5" />}
              <p className="text-sm text-gray-700">{rec.message}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Detailed Results */}
      <div className="card">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Detailed Validation Results</h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Historical Treasure Locations</h4>
            <div className="space-y-2">
              {validationResults.baseAlgorithm.scores
                .filter(s => s.actual)
                .map((score, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm text-gray-700">{score.year} - {score.location}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">{score.score}%</span>
                      {score.score > 70 ? (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-600" />
                      )}
                    </div>
                  </div>
                ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">Algorithm Performance</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Base Algorithm Accuracy:</span>
                <span className="font-medium">{validationResults.baseAlgorithm.accuracy.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Enhanced Algorithm Accuracy:</span>
                <span className="font-medium">{validationResults.enhancedAlgorithm.accuracy.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Average Confidence:</span>
                <span className="font-medium">{validationResults.improvements.confidenceIncrease.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Pattern Factors:</span>
                <span className="font-medium">{validationResults.improvements.patternFactors}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AlgorithmValidation;
