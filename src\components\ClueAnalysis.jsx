import React, { useState, useRef } from 'react';
import { useClues } from '../context/ClueContext';
import { useTrails } from '../context/TrailContext';
import { Upload, FileText, Search, TrendingUp, Plus, X, Eye } from 'lucide-react';

function ClueAnalysis() {
  const { clues, addClue, updateClue, deleteClue, analyzeClueText, getAllKeywords } = useClues();
  const { updateTrailsWithClues } = useTrails();
  const [showAddForm, setShowAddForm] = useState(false);
  const [newClue, setNewClue] = useState({
    type: 'hint',
    content: '',
    keywords: [],
    confidence: 5
  });
  const [selectedFile, setSelectedFile] = useState(null);
  const fileInputRef = useRef(null);

  const handleAddClue = () => {
    if (newClue.content.trim()) {
      const analysis = analyzeClueText(newClue.content);
      const clue = addClue({
        ...newClue,
        keywords: [...newClue.keywords, ...analysis.keywords],
        analysis: `Auto-analyzed: ${analysis.keywords.length} keywords found`
      });
      
      // Update trail scores with new clue
      updateTrailsWithClues(clues.concat([clue]));
      
      setNewClue({ type: 'hint', content: '', keywords: [], confidence: 5 });
      setShowAddForm(false);
    }
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target.result;
        setNewClue(prev => ({ ...prev, content }));
        setShowAddForm(true);
      };
      reader.readAsText(file);
    }
  };

  const handleKeywordAdd = (keyword) => {
    if (keyword && !newClue.keywords.includes(keyword)) {
      setNewClue(prev => ({
        ...prev,
        keywords: [...prev.keywords, keyword]
      }));
    }
  };

  const handleKeywordRemove = (keyword) => {
    setNewClue(prev => ({
      ...prev,
      keywords: prev.keywords.filter(k => k !== keyword)
    }));
  };

  const topKeywords = getAllKeywords().slice(0, 10);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Clue Analysis Module</h1>
          <p className="text-gray-600">Upload and analyze treasure hunt clues to update trail probability scores</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={() => fileInputRef.current?.click()}
            className="btn-secondary flex items-center space-x-2"
          >
            <Upload className="w-4 h-4" />
            <span>Upload File</span>
          </button>
          
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Add Clue</span>
          </button>
        </div>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept=".txt,.pdf,.doc,.docx"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* Add Clue Form */}
      {showAddForm && (
        <div className="card bg-gradient-to-r from-utah-50 to-treasure-50 border-utah-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-900">Add New Clue</h2>
            <button
              onClick={() => setShowAddForm(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Clue Type</label>
                <select
                  value={newClue.type}
                  onChange={(e) => setNewClue(prev => ({ ...prev, type: e.target.value }))}
                  className="input-field"
                >
                  <option value="hint">Hint</option>
                  <option value="riddle">Riddle</option>
                  <option value="visual">Visual Clue</option>
                  <option value="poem">Poem</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Confidence Level: {newClue.confidence}
                </label>
                <input
                  type="range"
                  min="1"
                  max="10"
                  value={newClue.confidence}
                  onChange={(e) => setNewClue(prev => ({ ...prev, confidence: parseInt(e.target.value) }))}
                  className="w-full"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Clue Content</label>
              <textarea
                value={newClue.content}
                onChange={(e) => setNewClue(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Enter the clue text, riddle, or hint..."
                rows={4}
                className="input-field"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Keywords</label>
              <div className="flex flex-wrap gap-2 mb-2">
                {newClue.keywords.map((keyword, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-treasure-100 text-treasure-800 rounded-full text-sm flex items-center space-x-1"
                  >
                    <span>{keyword}</span>
                    <button
                      onClick={() => handleKeywordRemove(keyword)}
                      className="text-treasure-600 hover:text-treasure-800"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
              
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder="Add keyword..."
                  className="input-field flex-1"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleKeywordAdd(e.target.value);
                      e.target.value = '';
                    }
                  }}
                />
                <button
                  onClick={() => {
                    const analysis = analyzeClueText(newClue.content);
                    setNewClue(prev => ({
                      ...prev,
                      keywords: [...new Set([...prev.keywords, ...analysis.keywords])]
                    }));
                  }}
                  className="btn-secondary"
                >
                  Auto-Extract
                </button>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={handleAddClue}
                className="btn-primary"
              >
                Add Clue
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Statistics Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card">
          <div className="flex items-center space-x-3">
            <FileText className="w-8 h-8 text-utah-600" />
            <div>
              <p className="text-sm text-gray-600">Total Clues</p>
              <p className="text-2xl font-bold text-gray-900">{clues.length}</p>
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="flex items-center space-x-3">
            <Search className="w-8 h-8 text-treasure-600" />
            <div>
              <p className="text-sm text-gray-600">Unique Keywords</p>
              <p className="text-2xl font-bold text-gray-900">{topKeywords.length}</p>
            </div>
          </div>
        </div>
        
        <div className="card">
          <div className="flex items-center space-x-3">
            <TrendingUp className="w-8 h-8 text-green-600" />
            <div>
              <p className="text-sm text-gray-600">Recent Clues</p>
              <p className="text-2xl font-bold text-gray-900">
                {clues.filter(c => {
                  const weekAgo = new Date();
                  weekAgo.setDate(weekAgo.getDate() - 7);
                  return new Date(c.dateAdded) >= weekAgo;
                }).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Top Keywords */}
      <div className="card">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Most Frequent Keywords</h2>
        <div className="flex flex-wrap gap-2">
          {topKeywords.map(({ keyword, count }) => (
            <span
              key={keyword}
              className="px-3 py-2 bg-gray-100 text-gray-800 rounded-lg text-sm font-medium"
            >
              {keyword} ({count})
            </span>
          ))}
        </div>
      </div>

      {/* Clues List */}
      <div className="card">
        <h2 className="text-xl font-bold text-gray-900 mb-6">All Clues</h2>
        
        <div className="space-y-4">
          {clues.map((clue) => (
            <div key={clue.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    clue.type === 'riddle' ? 'bg-purple-100 text-purple-800' :
                    clue.type === 'hint' ? 'bg-blue-100 text-blue-800' :
                    clue.type === 'visual' ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {clue.type.charAt(0).toUpperCase() + clue.type.slice(1)}
                  </span>
                  
                  <span className="text-sm text-gray-500">{clue.dateAdded}</span>
                  
                  <div className="flex items-center space-x-1">
                    <span className="text-sm text-gray-500">Confidence:</span>
                    <div className="flex space-x-1">
                      {[...Array(10)].map((_, i) => (
                        <div
                          key={i}
                          className={`w-2 h-2 rounded-full ${
                            i < clue.confidence ? 'bg-treasure-400' : 'bg-gray-200'
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={() => deleteClue(clue.id)}
                  className="text-red-500 hover:text-red-700"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              
              <p className="text-gray-700 mb-3">{clue.content}</p>
              
              {clue.keywords && clue.keywords.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-2">
                  {clue.keywords.map((keyword, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              )}
              
              {clue.analysis && (
                <p className="text-sm text-gray-600 italic">{clue.analysis}</p>
              )}
            </div>
          ))}
          
          {clues.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No clues added yet. Upload a file or add a clue manually to get started.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default ClueAnalysis;
