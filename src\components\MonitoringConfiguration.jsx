import React, { useState, useEffect } from 'react';
import { <PERSON>ting<PERSON>, Save, RotateCcw, AlertCircle, CheckCircle, Globe, Clock, Shield } from 'lucide-react';

function MonitoringConfiguration({ onSave, onCancel, initialConfig = {} }) {
  const [config, setConfig] = useState({
    checkInterval: 30, // minutes
    activeHours: {
      enabled: true,
      start: '06:00',
      end: '22:00',
      timezone: 'America/Denver'
    },
    rateLimiting: {
      delayBetweenRequests: 30, // seconds
      maxRetries: 3,
      backoffMultiplier: 2
    },
    contentFiltering: {
      minConfidenceThreshold: 0.6,
      autoApproveThreshold: 0.8,
      keywordFiltering: true,
      duplicateDetection: true
    },
    notifications: {
      enabled: true,
      newClueDetected: true,
      needsReview: true,
      integrationSuccess: true,
      errors: true,
      email: '',
      webhook: ''
    },
    targets: [
      {
        id: 'instagram_cline',
        name: '<PERSON> Instagram',
        url: 'https://www.instagram.com/the.cline.fam/',
        enabled: true,
        priority: 'high',
        checkInterval: 30,
        keywords: ['treasure', 'hunt', 'clue', 'hint', 'riddle', 'utah']
      },
      {
        id: 'abc4_news',
        name: 'ABC4 Utah Treasure Hunt',
        url: 'https://www.abc4.com/news/utah-treasure-hunt/',
        enabled: true,
        priority: 'high',
        checkInterval: 60,
        keywords: ['treasure hunt', 'clue', 'hint', 'david cline', 'john maxim']
      },
      {
        id: 'ksl_news',
        name: 'KSL News',
        url: 'https://www.ksl.com/search?q=utah+treasure+hunt',
        enabled: true,
        priority: 'medium',
        checkInterval: 120,
        keywords: ['utah treasure hunt', 'treasure found', 'clue released']
      },
      {
        id: 'fox13_news',
        name: 'Fox13 Utah',
        url: 'https://www.fox13now.com/search?q=utah+treasure+hunt',
        enabled: false,
        priority: 'medium',
        checkInterval: 120,
        keywords: ['utah treasure hunt', 'treasure hunting', 'clue']
      },
      {
        id: 'deseret_news',
        name: 'Deseret News',
        url: 'https://www.deseret.com/search?q=utah+treasure+hunt',
        enabled: false,
        priority: 'low',
        checkInterval: 240,
        keywords: ['utah treasure hunt', 'treasure', 'hunt']
      }
    ],
    ...initialConfig
  });

  const [errors, setErrors] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setHasChanges(JSON.stringify(config) !== JSON.stringify(initialConfig));
  }, [config, initialConfig]);

  const validateConfig = () => {
    const newErrors = {};

    if (config.checkInterval < 5 || config.checkInterval > 1440) {
      newErrors.checkInterval = 'Check interval must be between 5 and 1440 minutes';
    }

    if (config.rateLimiting.delayBetweenRequests < 10) {
      newErrors.rateLimiting = 'Delay between requests must be at least 10 seconds';
    }

    if (config.contentFiltering.minConfidenceThreshold < 0 || config.contentFiltering.minConfidenceThreshold > 1) {
      newErrors.contentFiltering = 'Confidence threshold must be between 0 and 1';
    }

    if (config.notifications.email && !/\S+@\S+\.\S+/.test(config.notifications.email)) {
      newErrors.email = 'Invalid email address';
    }

    if (config.notifications.webhook && !/^https?:\/\/.+/.test(config.notifications.webhook)) {
      newErrors.webhook = 'Webhook URL must be a valid HTTP/HTTPS URL';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateConfig()) {
      onSave(config);
    }
  };

  const handleReset = () => {
    setConfig(initialConfig);
    setErrors({});
  };

  const updateConfig = (path, value) => {
    setConfig(prev => {
      const newConfig = { ...prev };
      const keys = path.split('.');
      let current = newConfig;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  const updateTarget = (targetId, updates) => {
    setConfig(prev => ({
      ...prev,
      targets: prev.targets.map(target =>
        target.id === targetId ? { ...target, ...updates } : target
      )
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Settings className="w-6 h-6 text-gray-600" />
          <h2 className="text-xl font-bold text-gray-900">Monitoring Configuration</h2>
        </div>
        
        <div className="flex items-center space-x-3">
          {hasChanges && (
            <span className="text-sm text-yellow-600 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              Unsaved changes
            </span>
          )}
          
          <button
            onClick={handleReset}
            className="btn-secondary flex items-center space-x-2"
          >
            <RotateCcw className="w-4 h-4" />
            <span>Reset</span>
          </button>
          
          <button
            onClick={handleSave}
            className="btn-primary flex items-center space-x-2"
          >
            <Save className="w-4 h-4" />
            <span>Save Configuration</span>
          </button>
        </div>
      </div>

      {/* General Settings */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Clock className="w-5 h-5 mr-2" />
          General Settings
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Check Interval (minutes)
            </label>
            <input
              type="number"
              min="5"
              max="1440"
              value={config.checkInterval}
              onChange={(e) => updateConfig('checkInterval', parseInt(e.target.value))}
              className={`input-field ${errors.checkInterval ? 'border-red-300' : ''}`}
            />
            {errors.checkInterval && (
              <p className="text-sm text-red-600 mt-1">{errors.checkInterval}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Timezone
            </label>
            <select
              value={config.activeHours.timezone}
              onChange={(e) => updateConfig('activeHours.timezone', e.target.value)}
              className="input-field"
            >
              <option value="America/Denver">Mountain Time (Denver)</option>
              <option value="America/New_York">Eastern Time</option>
              <option value="America/Chicago">Central Time</option>
              <option value="America/Los_Angeles">Pacific Time</option>
            </select>
          </div>
        </div>

        <div className="mt-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.activeHours.enabled}
              onChange={(e) => updateConfig('activeHours.enabled', e.target.checked)}
              className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
            />
            <span className="text-sm text-gray-700">Enable active hours monitoring</span>
          </label>
          
          {config.activeHours.enabled && (
            <div className="mt-2 grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-gray-600 mb-1">Start Time</label>
                <input
                  type="time"
                  value={config.activeHours.start}
                  onChange={(e) => updateConfig('activeHours.start', e.target.value)}
                  className="input-field"
                />
              </div>
              <div>
                <label className="block text-sm text-gray-600 mb-1">End Time</label>
                <input
                  type="time"
                  value={config.activeHours.end}
                  onChange={(e) => updateConfig('activeHours.end', e.target.value)}
                  className="input-field"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Rate Limiting */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Shield className="w-5 h-5 mr-2" />
          Rate Limiting & Safety
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Delay Between Requests (seconds)
            </label>
            <input
              type="number"
              min="10"
              max="300"
              value={config.rateLimiting.delayBetweenRequests}
              onChange={(e) => updateConfig('rateLimiting.delayBetweenRequests', parseInt(e.target.value))}
              className={`input-field ${errors.rateLimiting ? 'border-red-300' : ''}`}
            />
            {errors.rateLimiting && (
              <p className="text-sm text-red-600 mt-1">{errors.rateLimiting}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Retries
            </label>
            <input
              type="number"
              min="1"
              max="10"
              value={config.rateLimiting.maxRetries}
              onChange={(e) => updateConfig('rateLimiting.maxRetries', parseInt(e.target.value))}
              className="input-field"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Backoff Multiplier
            </label>
            <input
              type="number"
              min="1"
              max="5"
              step="0.1"
              value={config.rateLimiting.backoffMultiplier}
              onChange={(e) => updateConfig('rateLimiting.backoffMultiplier', parseFloat(e.target.value))}
              className="input-field"
            />
          </div>
        </div>
      </div>

      {/* Content Filtering */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Content Filtering</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Min Confidence Threshold
            </label>
            <input
              type="number"
              min="0"
              max="1"
              step="0.1"
              value={config.contentFiltering.minConfidenceThreshold}
              onChange={(e) => updateConfig('contentFiltering.minConfidenceThreshold', parseFloat(e.target.value))}
              className={`input-field ${errors.contentFiltering ? 'border-red-300' : ''}`}
            />
            {errors.contentFiltering && (
              <p className="text-sm text-red-600 mt-1">{errors.contentFiltering}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Auto-approve Threshold
            </label>
            <input
              type="number"
              min="0"
              max="1"
              step="0.1"
              value={config.contentFiltering.autoApproveThreshold}
              onChange={(e) => updateConfig('contentFiltering.autoApproveThreshold', parseFloat(e.target.value))}
              className="input-field"
            />
          </div>
        </div>
        
        <div className="mt-4 space-y-2">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.contentFiltering.keywordFiltering}
              onChange={(e) => updateConfig('contentFiltering.keywordFiltering', e.target.checked)}
              className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
            />
            <span className="text-sm text-gray-700">Enable keyword filtering</span>
          </label>
          
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.contentFiltering.duplicateDetection}
              onChange={(e) => updateConfig('contentFiltering.duplicateDetection', e.target.checked)}
              className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
            />
            <span className="text-sm text-gray-700">Enable duplicate detection</span>
          </label>
        </div>
      </div>

      {/* Monitoring Targets */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Globe className="w-5 h-5 mr-2" />
          Monitoring Targets
        </h3>
        
        <div className="space-y-4">
          {config.targets.map((target) => (
            <div key={target.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={target.enabled}
                      onChange={(e) => updateTarget(target.id, { enabled: e.target.checked })}
                      className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
                    />
                    <span className="font-medium text-gray-900">{target.name}</span>
                  </label>
                </div>
                
                <select
                  value={target.priority}
                  onChange={(e) => updateTarget(target.id, { priority: e.target.value })}
                  className="text-sm border border-gray-300 rounded px-2 py-1"
                >
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm text-gray-600 mb-1">Check Interval (minutes)</label>
                  <input
                    type="number"
                    min="5"
                    max="1440"
                    value={target.checkInterval}
                    onChange={(e) => updateTarget(target.id, { checkInterval: parseInt(e.target.value) })}
                    className="input-field text-sm"
                    disabled={!target.enabled}
                  />
                </div>
                
                <div>
                  <label className="block text-sm text-gray-600 mb-1">Keywords</label>
                  <input
                    type="text"
                    value={target.keywords.join(', ')}
                    onChange={(e) => updateTarget(target.id, { keywords: e.target.value.split(', ').filter(k => k.trim()) })}
                    className="input-field text-sm"
                    placeholder="keyword1, keyword2, keyword3"
                    disabled={!target.enabled}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Notifications */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Notifications</h3>
        
        <div className="space-y-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={config.notifications.enabled}
              onChange={(e) => updateConfig('notifications.enabled', e.target.checked)}
              className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
            />
            <span className="text-sm text-gray-700">Enable notifications</span>
          </label>
          
          {config.notifications.enabled && (
            <div className="ml-6 space-y-3">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.notifications.newClueDetected}
                  onChange={(e) => updateConfig('notifications.newClueDetected', e.target.checked)}
                  className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
                />
                <span className="text-sm text-gray-700">New clue detected</span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.notifications.needsReview}
                  onChange={(e) => updateConfig('notifications.needsReview', e.target.checked)}
                  className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
                />
                <span className="text-sm text-gray-700">Clue needs review</span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.notifications.integrationSuccess}
                  onChange={(e) => updateConfig('notifications.integrationSuccess', e.target.checked)}
                  className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
                />
                <span className="text-sm text-gray-700">Integration success</span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={config.notifications.errors}
                  onChange={(e) => updateConfig('notifications.errors', e.target.checked)}
                  className="rounded border-gray-300 text-treasure-600 focus:ring-treasure-500"
                />
                <span className="text-sm text-gray-700">Errors and warnings</span>
              </label>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-4">
                <div>
                  <label className="block text-sm text-gray-600 mb-1">Email (optional)</label>
                  <input
                    type="email"
                    value={config.notifications.email}
                    onChange={(e) => updateConfig('notifications.email', e.target.value)}
                    className={`input-field ${errors.email ? 'border-red-300' : ''}`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="text-sm text-red-600 mt-1">{errors.email}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-sm text-gray-600 mb-1">Webhook URL (optional)</label>
                  <input
                    type="url"
                    value={config.notifications.webhook}
                    onChange={(e) => updateConfig('notifications.webhook', e.target.value)}
                    className={`input-field ${errors.webhook ? 'border-red-300' : ''}`}
                    placeholder="https://your-webhook-url.com"
                  />
                  {errors.webhook && (
                    <p className="text-sm text-red-600 mt-1">{errors.webhook}</p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default MonitoringConfiguration;
