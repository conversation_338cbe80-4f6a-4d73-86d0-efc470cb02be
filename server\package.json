{"name": "utah-treasure-finder-server", "version": "1.0.0", "description": "Backend API server for Utah Treasure Finder application", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["utah", "treasure", "hunting", "trails", "api", "express"], "author": "Utah Treasure Finder Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1"}}