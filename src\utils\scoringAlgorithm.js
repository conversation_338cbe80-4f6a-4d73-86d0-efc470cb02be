import { scoringWeights } from '../data/utahTrails';

/**
 * Calculate treasure likelihood score for a trail based on historical patterns
 * @param {Object} trail - Trail object with characteristics
 * @returns {Object} - Score breakdown and total
 */
export function calculateTrailScore(trail) {
  const scores = {
    wasatchFront: 0,
    elevationRange: 0,
    establishedTrail: 0,
    hikeTimeRange: 0,
    total: 0
  };

  // Wasatch Front proximity (40% weight)
  scores.wasatchFront = trail.wasatchFront ? 100 : 0;

  // Elevation range 4,500-7,000 feet (25% weight)
  if (trail.elevation >= 4500 && trail.elevation <= 7000) {
    // Perfect range gets full score
    scores.elevationRange = 100;
  } else if (trail.elevation >= 4000 && trail.elevation <= 8000) {
    // Extended range gets partial score
    scores.elevationRange = 60;
  } else {
    scores.elevationRange = 20;
  }

  // Established trail system (20% weight)
  scores.establishedTrail = trail.establishedTrail ? 100 : 0;

  // 30-90 minute hike accessibility (15% weight)
  if (trail.hikeTime >= 30 && trail.hikeTime <= 90) {
    scores.hikeTimeRange = 100;
  } else if (trail.hikeTime >= 15 && trail.hikeTime <= 120) {
    scores.hikeTimeRange = 70;
  } else if (trail.hikeTime >= 0 && trail.hikeTime <= 180) {
    scores.hikeTimeRange = 40;
  } else {
    scores.hikeTimeRange = 10;
  }

  // Calculate weighted total
  scores.total = (
    scores.wasatchFront * scoringWeights.wasatchFront +
    scores.elevationRange * scoringWeights.elevationRange +
    scores.establishedTrail * scoringWeights.establishedTrail +
    scores.hikeTimeRange * scoringWeights.hikeTimeRange
  );

  return scores;
}

/**
 * Update trail scores based on clue analysis
 * @param {Array} trails - Array of trail objects
 * @param {Array} clues - Array of clue objects
 * @returns {Array} - Updated trails with clue-adjusted scores
 */
export function updateScoresWithClues(trails, clues) {
  return trails.map(trail => {
    const baseScore = calculateTrailScore(trail);
    let clueBonus = 0;
    let matchedClues = [];

    clues.forEach(clue => {
      const bonus = analyzeClueMatch(trail, clue);
      if (bonus > 0) {
        clueBonus += bonus;
        matchedClues.push({
          clueId: clue.id,
          bonus: bonus,
          reason: clue.keywords.filter(keyword => 
            trailMatchesKeyword(trail, keyword)
          )
        });
      }
    });

    return {
      ...trail,
      baseScore: baseScore.total,
      clueBonus: Math.min(clueBonus, 50), // Cap bonus at 50 points
      totalScore: Math.min(baseScore.total + clueBonus, 100),
      matchedClues: matchedClues,
      scoreBreakdown: baseScore
    };
  });
}

/**
 * Analyze how well a trail matches a specific clue
 * @param {Object} trail - Trail object
 * @param {Object} clue - Clue object with keywords and analysis
 * @returns {number} - Bonus points (0-20)
 */
function analyzeClueMatch(trail, clue) {
  let bonus = 0;
  
  if (!clue.keywords || clue.keywords.length === 0) return 0;

  clue.keywords.forEach(keyword => {
    if (trailMatchesKeyword(trail, keyword)) {
      bonus += clue.confidence || 5; // Default 5 points per match
    }
  });

  return Math.min(bonus, 20); // Max 20 points per clue
}

/**
 * Check if a trail matches a specific keyword
 * @param {Object} trail - Trail object
 * @param {string} keyword - Keyword to match
 * @returns {boolean} - Whether trail matches keyword
 */
function trailMatchesKeyword(trail, keyword) {
  const searchText = `
    ${trail.name} 
    ${trail.location} 
    ${trail.trailType} 
    ${trail.features?.join(' ') || ''} 
    ${trail.description}
  `.toLowerCase();

  return searchText.includes(keyword.toLowerCase());
}

/**
 * Get confidence level based on score
 * @param {number} score - Trail score (0-100)
 * @returns {string} - Confidence level
 */
export function getConfidenceLevel(score) {
  if (score >= 80) return 'Very High';
  if (score >= 65) return 'High';
  if (score >= 50) return 'Medium';
  if (score >= 35) return 'Low';
  return 'Very Low';
}

/**
 * Get color coding for score visualization
 * @param {number} score - Trail score (0-100)
 * @returns {string} - CSS color class
 */
export function getScoreColor(score) {
  if (score >= 80) return 'text-red-600 bg-red-100';
  if (score >= 65) return 'text-orange-600 bg-orange-100';
  if (score >= 50) return 'text-yellow-600 bg-yellow-100';
  if (score >= 35) return 'text-blue-600 bg-blue-100';
  return 'text-gray-600 bg-gray-100';
}
