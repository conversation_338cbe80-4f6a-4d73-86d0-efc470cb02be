import React, { useState, useEffect } from 'react';
import { useTrails } from '../context/TrailContext';
import { Brain, TrendingUp, Target, AlertCircle, CheckCircle, Activity, BarChart3, Zap, TestTube } from 'lucide-react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import MLValidationTester from './MLValidationTester';

function MLInsightsDashboard() {
  const { trails, getMLStatus, validateMLAlgorithm, updateMLPerformance, exportMLModels, importMLModels } = useTrails();
  const [mlStatus, setMLStatus] = useState(null);
  const [validationResults, setValidationResults] = useState(null);
  const [isValidating, setIsValidating] = useState(false);
  const [performanceHistory, setPerformanceHistory] = useState([]);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadMLStatus();
    generatePerformanceHistory();
  }, [trails]);

  const loadMLStatus = () => {
    try {
      const status = getMLStatus();
      setMLStatus(status);
    } catch (error) {
      console.error('Failed to load ML status:', error);
    }
  };

  const runValidation = async () => {
    setIsValidating(true);
    try {
      const results = await validateMLAlgorithm();
      setValidationResults(results);
    } catch (error) {
      console.error('Validation failed:', error);
      setValidationResults({ error: error.message, accuracy: 0 });
    } finally {
      setIsValidating(false);
    }
  };

  const generatePerformanceHistory = () => {
    // Generate mock performance history for demonstration
    const history = [];
    const baseDate = new Date();
    
    for (let i = 7; i >= 0; i--) {
      const date = new Date(baseDate);
      date.setDate(date.getDate() - i);
      
      history.push({
        date: date.toISOString().split('T')[0],
        accuracy: 85 + Math.random() * 10 + (i < 4 ? 5 : 0), // Improving over time
        confidence: 70 + Math.random() * 15 + (i < 4 ? 8 : 0),
        processingTime: 300 + Math.random() * 200 - (i < 4 ? 50 : 0), // Getting faster
        predictions: Math.floor(50 + Math.random() * 20)
      });
    }
    
    setPerformanceHistory(history);
  };

  const handleFileImport = async (event) => {
    const file = event.target.files[0];
    if (file) {
      try {
        const success = await importMLModels(file);
        if (success) {
          alert('ML models imported successfully!');
          loadMLStatus();
        } else {
          alert('Failed to import ML models');
        }
      } catch (error) {
        alert(`Import failed: ${error.message}`);
      }
    }
  };

  const getMLInsights = () => {
    const trailsWithML = trails.filter(trail => trail.mlInsights);
    
    if (trailsWithML.length === 0) return null;

    const insights = {
      totalAnalyzed: trailsWithML.length,
      highConfidence: trailsWithML.filter(t => t.confidence > 0.8).length,
      averageConfidence: trailsWithML.reduce((sum, t) => sum + t.confidence, 0) / trailsWithML.length,
      topRecommendations: trailsWithML
        .filter(t => t.mlInsights?.recommendations?.length > 0)
        .slice(0, 5)
        .map(t => ({
          name: t.name,
          score: t.totalScore,
          recommendations: t.mlInsights.recommendations
        }))
    };

    return insights;
  };

  const mlInsights = getMLInsights();
  const COLORS = ['#eab308', '#3b82f6', '#10b981', '#f59e0b', '#ef4444'];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Brain className="w-6 h-6 mr-2 text-blue-600" />
            ML Insights Dashboard
          </h2>
          <p className="text-gray-600">Advanced machine learning analytics and performance monitoring</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <input
            type="file"
            accept=".json"
            onChange={handleFileImport}
            className="hidden"
            id="ml-import"
          />
          <label htmlFor="ml-import" className="btn-secondary cursor-pointer">
            Import Models
          </label>
          
          <button
            onClick={exportMLModels}
            className="btn-secondary"
          >
            Export Models
          </button>
          
          <button
            onClick={runValidation}
            disabled={isValidating}
            className="btn-primary flex items-center space-x-2"
          >
            {isValidating ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Target className="w-4 h-4" />
            )}
            <span>Validate Algorithm</span>
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'overview'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Activity className="w-4 h-4 inline mr-2" />
            Overview
          </button>

          <button
            onClick={() => setActiveTab('validation')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'validation'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <TestTube className="w-4 h-4 inline mr-2" />
            Validation & Testing
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'validation' ? (
        <MLValidationTester />
      ) : (
        <div className="space-y-6">
          {/* ML Status Overview */}
      {mlStatus && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="card">
            <div className="flex items-center space-x-3">
              <Activity className={`w-8 h-8 ${mlStatus.enabled ? 'text-green-600' : 'text-gray-400'}`} />
              <div>
                <p className="text-sm text-gray-600">ML Status</p>
                <p className="text-lg font-bold text-gray-900">
                  {mlStatus.enabled ? 'Active' : 'Inactive'}
                </p>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="flex items-center space-x-3">
              <TrendingUp className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Accuracy</p>
                <p className="text-lg font-bold text-gray-900">
                  {(mlStatus.performanceMetrics.accuracy * 100).toFixed(1)}%
                </p>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="flex items-center space-x-3">
              <Zap className="w-8 h-8 text-yellow-600" />
              <div>
                <p className="text-sm text-gray-600">Processing Time</p>
                <p className="text-lg font-bold text-gray-900">
                  {mlStatus.performanceMetrics.processingTime.toFixed(0)}ms
                </p>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="flex items-center space-x-3">
              <BarChart3 className="w-8 h-8 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Memory Usage</p>
                <p className="text-lg font-bold text-gray-900">
                  {mlStatus.performanceMetrics.memoryUsage.toFixed(1)}MB
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Performance Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Accuracy Trend</h3>
          <ResponsiveContainer width="100%" height={250}>
            <LineChart data={performanceHistory}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis domain={[80, 100]} />
              <Tooltip />
              <Line type="monotone" dataKey="accuracy" stroke="#3b82f6" strokeWidth={2} />
              <Line type="monotone" dataKey="confidence" stroke="#10b981" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Processing Performance</h3>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={performanceHistory}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="processingTime" fill="#eab308" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* ML Insights Summary */}
      {mlInsights && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Current ML Insights</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">{mlInsights.totalAnalyzed}</p>
              <p className="text-sm text-gray-600">Trails Analyzed</p>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">{mlInsights.highConfidence}</p>
              <p className="text-sm text-gray-600">High Confidence</p>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <p className="text-2xl font-bold text-purple-600">{(mlInsights.averageConfidence * 100).toFixed(1)}%</p>
              <p className="text-sm text-gray-600">Avg Confidence</p>
            </div>
          </div>

          {mlInsights.topRecommendations.length > 0 && (
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Top ML Recommendations</h4>
              <div className="space-y-3">
                {mlInsights.topRecommendations.map((trail, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium text-gray-900">{trail.name}</h5>
                      <span className="text-sm font-medium text-treasure-600">{trail.score.toFixed(1)}</span>
                    </div>
                    <div className="space-y-1">
                      {trail.recommendations.slice(0, 2).map((rec, recIndex) => (
                        <div key={recIndex} className="flex items-center space-x-2">
                          {rec.priority === 'high' && <AlertCircle className="w-4 h-4 text-red-500" />}
                          {rec.priority === 'medium' && <CheckCircle className="w-4 h-4 text-yellow-500" />}
                          {rec.priority === 'low' && <CheckCircle className="w-4 h-4 text-green-500" />}
                          <span className="text-sm text-gray-700">{rec.message}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Validation Results */}
      {validationResults && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Algorithm Validation Results</h3>
          
          {validationResults.error ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-600" />
                <span className="text-red-800">Validation Error: {validationResults.error}</span>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">
                    {(validationResults.accuracy * 100).toFixed(1)}%
                  </p>
                  <p className="text-sm text-gray-600">Overall Accuracy</p>
                </div>
                
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">
                    {validationResults.validationResults?.length || 0}
                  </p>
                  <p className="text-sm text-gray-600">Test Cases</p>
                </div>
                
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <p className="text-2xl font-bold text-purple-600">
                    {validationResults.mlValidation?.averageConfidence?.toFixed(1) || 'N/A'}%
                  </p>
                  <p className="text-sm text-gray-600">Avg Confidence</p>
                </div>
              </div>

              {validationResults.validationResults && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Historical Location Validation</h4>
                  <div className="space-y-2">
                    {validationResults.validationResults.map((result, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <span className="font-medium text-gray-900">{result.year} - {result.location}</span>
                          <span className="ml-2 text-sm text-gray-600">({result.algorithm})</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className="text-sm font-medium">{result.score.toFixed(1)}</span>
                          <span className="text-sm text-gray-600">{(result.confidence * 100).toFixed(0)}% conf</span>
                          {result.score > 70 ? (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          ) : (
                            <AlertCircle className="w-5 h-5 text-red-600" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Model Status */}
      {mlStatus?.serviceStatus && (
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Model Status</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Bayesian Ensemble</h4>
              <div className="space-y-1 text-sm">
                <p className="text-gray-600">
                  Models: {mlStatus.serviceStatus.modelStatus.bayesianEnsemble?.length || 0}
                </p>
                <p className="text-gray-600">
                  Status: {mlStatus.serviceStatus.isInitialized ? 'Active' : 'Inactive'}
                </p>
              </div>
            </div>
            
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">NLP Analyzer</h4>
              <div className="space-y-1 text-sm">
                <p className="text-gray-600">
                  Vocabulary: {mlStatus.serviceStatus.modelStatus.nlpAnalyzer ? 'Loaded' : 'Not Loaded'}
                </p>
                <p className="text-gray-600">
                  Status: {mlStatus.serviceStatus.modelStatus.nlpAnalyzer ? 'Active' : 'Inactive'}
                </p>
              </div>
            </div>
            
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Geospatial Analyzer</h4>
              <div className="space-y-1 text-sm">
                <p className="text-gray-600">
                  GMM Model: {mlStatus.serviceStatus.modelStatus.geospatialAnalyzer ? 'Trained' : 'Not Trained'}
                </p>
                <p className="text-gray-600">
                  Status: {mlStatus.serviceStatus.modelStatus.geospatialAnalyzer ? 'Active' : 'Inactive'}
                </p>
              </div>
            </div>
          </div>
        </div>
        </div>
      )}
    </div>
  );
}

export default MLInsightsDashboard;
