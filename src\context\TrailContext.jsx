import React, { createContext, useContext, useState, useEffect } from 'react';
import { utahTrails } from '../data/utahTrails';
import { calculateTrailScore, updateScoresWithClues } from '../utils/scoringAlgorithm';
import enhancedScoringAlgorithm from '../utils/enhancedScoringAlgorithm';
import patternRecognitionEngine from '../services/patternRecognitionEngine';

const TrailContext = createContext();

export function useTrails() {
  const context = useContext(TrailContext);
  if (!context) {
    throw new Error('useTrails must be used within a TrailProvider');
  }
  return context;
}

export function TrailProvider({ children }) {
  const [trails, setTrails] = useState([]);
  const [filteredTrails, setFilteredTrails] = useState([]);
  const [filters, setFilters] = useState({
    difficulty: 'all',
    region: 'all',
    scoreRange: [0, 100],
    wasatchFrontOnly: false
  });
  const [sortBy, setSortBy] = useState('score'); // score, name, elevation, difficulty
  const [useEnhancedScoring, setUseEnhancedScoring] = useState(false);
  const [patternData, setPatternData] = useState(null);
  const [communityTheories, setCommunityTheories] = useState([]);

  // Initialize trails with scores and ML services
  useEffect(() => {
    const initializeTrailsAndML = async () => {
      // Initialize trails with base scores
      const trailsWithScores = utahTrails.map(trail => {
        const scoreBreakdown = calculateTrailScore(trail);
        return {
          ...trail,
          baseScore: scoreBreakdown.total,
          totalScore: scoreBreakdown.total,
          scoreBreakdown: scoreBreakdown,
          clueBonus: 0,
          matchedClues: [],
          enhancedScore: null,
          confidence: 0.5,
          mlInsights: null
        };
      });
      setTrails(trailsWithScores);

      // Initialize ML services with historical data
      try {
        const historicalData = {
          textDocuments: [
            { id: 'riddle_2020', text: 'Si sufres dolor, busca el atajo', type: 'riddle' },
            { id: 'riddle_2021', text: 'Where time stands still and nature calls', type: 'riddle' },
            { id: 'riddle_2022', text: 'Begin your search where pioneers rest', type: 'riddle' },
            { id: 'riddle_2023', text: 'The treasure lies where eagles soar', type: 'riddle' }
          ],
          treasureLocations: [
            { lat: 40.6089, lon: -111.7910, year: 2020, metadata: { location: 'Rocky Mouth Canyon' } },
            { lat: 40.6847, lon: -111.8398, year: 2021, metadata: { location: 'Heughs Canyon Trail' } },
            { lat: 41.3847, lon: -111.8398, year: 2022, metadata: { location: 'Ben Lomond Peak' } },
            { lat: 40.8847, lon: -111.8398, year: 2023, metadata: { location: 'Mueller Park Trail' } }
          ]
        };

        const mlInitialized = await enhancedScoringAlgorithm.initializeMLServices(historicalData);
        if (mlInitialized) {
          console.log('ML services initialized successfully');
          setUseEnhancedScoring(true); // Auto-enable ML if available
        }
      } catch (error) {
        console.error('Failed to initialize ML services:', error);
      }
    };

    initializeTrailsAndML();
  }, []);

  // Update scores when enhanced scoring is toggled or pattern data changes
  useEffect(() => {
    if (useEnhancedScoring) {
      updateTrailsWithEnhancedScoring();
    }
  }, [useEnhancedScoring, patternData]);

  // Apply filters and sorting
  useEffect(() => {
    let filtered = trails.filter(trail => {
      // Difficulty filter
      if (filters.difficulty !== 'all' && trail.difficulty !== filters.difficulty) {
        return false;
      }

      // Region filter (simplified - could be expanded)
      if (filters.region !== 'all') {
        if (filters.region === 'salt-lake' && !trail.location.includes('Salt Lake')) {
          return false;
        }
        if (filters.region === 'utah-county' && !trail.location.includes('American Fork')) {
          return false;
        }
      }

      // Score range filter
      if (trail.totalScore < filters.scoreRange[0] || trail.totalScore > filters.scoreRange[1]) {
        return false;
      }

      // Wasatch Front only filter
      if (filters.wasatchFrontOnly && !trail.wasatchFront) {
        return false;
      }

      return true;
    });

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'score':
          return b.totalScore - a.totalScore;
        case 'name':
          return a.name.localeCompare(b.name);
        case 'elevation':
          return b.elevation - a.elevation;
        case 'difficulty':
          const difficultyOrder = { 'Easy': 1, 'Moderate': 2, 'Hard': 3 };
          return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
        default:
          return b.totalScore - a.totalScore;
      }
    });

    setFilteredTrails(filtered);
  }, [trails, filters, sortBy]);

  const updateTrailsWithClues = (clues) => {
    if (useEnhancedScoring && patternData) {
      updateTrailsWithEnhancedScoring(clues);
    } else {
      const updatedTrails = updateScoresWithClues(trails, clues);
      setTrails(updatedTrails);
    }
  };

  const updateTrailsWithEnhancedScoring = async (clues = []) => {
    try {
      if (patternData) {
        enhancedScoringAlgorithm.loadPatterns(patternData);
      }

      // Process trails in batches to avoid blocking UI
      const batchSize = 3;
      const enhancedTrails = [...trails];

      for (let i = 0; i < trails.length; i += batchSize) {
        const batch = trails.slice(i, i + batchSize);

        const batchPromises = batch.map(async (trail, index) => {
          try {
            const enhancedScore = await enhancedScoringAlgorithm.calculateEnhancedScore(
              trail,
              clues,
              communityTheories
            );

            return {
              index: i + index,
              trail: {
                ...trail,
                enhancedScore: enhancedScore,
                totalScore: enhancedScore.total,
                confidence: enhancedScore.confidence,
                uncertainty: enhancedScore.uncertainty,
                credibleInterval: enhancedScore.credibleInterval,
                mlInsights: enhancedScore.mlInsights || null,
                patternMatches: enhancedScore.patterns?.breakdown || enhancedScore.ml?.breakdown || {},
                lastUpdated: new Date().toISOString()
              }
            };
          } catch (error) {
            console.error(`Error scoring trail ${trail.name}:`, error);
            return {
              index: i + index,
              trail: {
                ...trail,
                enhancedScore: { total: trail.baseScore, confidence: 0.3, error: error.message },
                totalScore: trail.baseScore,
                confidence: 0.3,
                lastUpdated: new Date().toISOString()
              }
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);

        // Update trails array with batch results
        batchResults.forEach(result => {
          enhancedTrails[result.index] = result.trail;
        });

        // Update state incrementally for better UX
        setTrails([...enhancedTrails]);

        // Small delay to prevent blocking
        if (i + batchSize < trails.length) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }

      console.log('Enhanced scoring completed for all trails');

    } catch (error) {
      console.error('Error in enhanced scoring:', error);
      // Fallback to base scoring
      const fallbackTrails = trails.map(trail => ({
        ...trail,
        enhancedScore: { total: trail.baseScore, confidence: 0.3, error: 'ML scoring failed' },
        totalScore: trail.baseScore,
        confidence: 0.3
      }));
      setTrails(fallbackTrails);
    }
  };

  const loadPatternData = async (collectedData) => {
    try {
      const patterns = await patternRecognitionEngine.analyzePatterns(collectedData);
      setPatternData(patterns);
      return patterns;
    } catch (error) {
      console.error('Failed to load pattern data:', error);
      return null;
    }
  };

  const toggleEnhancedScoring = (enabled) => {
    setUseEnhancedScoring(enabled);
  };

  const addCommunityTheory = (theory) => {
    setCommunityTheories(prev => [...prev, {
      id: Date.now(),
      ...theory,
      addedAt: new Date().toISOString()
    }]);
  };

  const getMLStatus = () => {
    return enhancedScoringAlgorithm.getMLStatus();
  };

  const validateMLAlgorithm = async () => {
    try {
      const historicalTreasures = [
        {
          year: 2020,
          location: 'Rocky Mouth Canyon',
          coordinates: [40.6089, -111.7910],
          elevation: 5200,
          hikeTime: 45,
          characteristics: { wasatchFront: true, establishedTrail: true }
        },
        {
          year: 2021,
          location: 'Heughs Canyon Trail',
          coordinates: [40.6847, -111.8398],
          elevation: 5800,
          hikeTime: 60,
          characteristics: { wasatchFront: true, establishedTrail: true }
        },
        {
          year: 2022,
          location: 'Ben Lomond Peak',
          coordinates: [41.3847, -111.8398],
          elevation: 6200,
          hikeTime: 75,
          characteristics: { wasatchFront: true, establishedTrail: true }
        },
        {
          year: 2023,
          location: 'Mueller Park Trail',
          coordinates: [40.8847, -111.8398],
          elevation: 5600,
          hikeTime: 50,
          characteristics: { wasatchFront: true, establishedTrail: true }
        }
      ];

      const validationResults = await enhancedScoringAlgorithm.validateAlgorithm(
        historicalTreasures,
        patternData
      );

      console.log('ML Algorithm Validation Results:', validationResults);
      return validationResults;
    } catch (error) {
      console.error('ML validation failed:', error);
      return { error: error.message, accuracy: 0 };
    }
  };

  const updateMLPerformance = async (performanceData) => {
    try {
      await enhancedScoringAlgorithm.updateMLModels(performanceData);
      console.log('ML models updated with performance feedback');
    } catch (error) {
      console.error('Failed to update ML performance:', error);
    }
  };

  const exportMLModels = () => {
    try {
      const modelData = enhancedScoringAlgorithm.exportMLModels();
      if (modelData) {
        const blob = new Blob([JSON.stringify(modelData, null, 2)], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ml-models-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        window.URL.revokeObjectURL(url);
        console.log('ML models exported successfully');
      }
    } catch (error) {
      console.error('Failed to export ML models:', error);
    }
  };

  const importMLModels = async (file) => {
    try {
      const text = await file.text();
      const modelData = JSON.parse(text);
      const success = await enhancedScoringAlgorithm.importMLModels(modelData);

      if (success) {
        console.log('ML models imported successfully');
        setUseEnhancedScoring(true);
        await updateTrailsWithEnhancedScoring();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to import ML models:', error);
      return false;
    }
  };

  const updateFilters = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const resetFilters = () => {
    setFilters({
      difficulty: 'all',
      region: 'all',
      scoreRange: [0, 100],
      wasatchFrontOnly: false
    });
  };

  const getTopTrails = (count = 5) => {
    return [...trails]
      .sort((a, b) => b.totalScore - a.totalScore)
      .slice(0, count);
  };

  const getTrailById = (id) => {
    return trails.find(trail => trail.id === id);
  };

  const value = {
    trails,
    filteredTrails,
    filters,
    sortBy,
    useEnhancedScoring,
    patternData,
    communityTheories,
    updateTrailsWithClues,
    updateFilters,
    resetFilters,
    setSortBy,
    getTopTrails,
    getTrailById,
    loadPatternData,
    toggleEnhancedScoring,
    addCommunityTheory,
    updateTrailsWithEnhancedScoring,
    // ML-specific methods
    getMLStatus,
    validateMLAlgorithm,
    updateMLPerformance,
    exportMLModels,
    importMLModels
  };

  return (
    <TrailContext.Provider value={value}>
      {children}
    </TrailContext.Provider>
  );
}
