import React, { createContext, useContext, useState, useEffect } from 'react';
import { utahTrails } from '../data/utahTrails';
import { calculateTrailScore, updateScoresWithClues } from '../utils/scoringAlgorithm';
import enhancedScoringAlgorithm from '../utils/enhancedScoringAlgorithm';
import patternRecognitionEngine from '../services/patternRecognitionEngine';

const TrailContext = createContext();

export function useTrails() {
  const context = useContext(TrailContext);
  if (!context) {
    throw new Error('useTrails must be used within a TrailProvider');
  }
  return context;
}

export function TrailProvider({ children }) {
  const [trails, setTrails] = useState([]);
  const [filteredTrails, setFilteredTrails] = useState([]);
  const [filters, setFilters] = useState({
    difficulty: 'all',
    region: 'all',
    scoreRange: [0, 100],
    wasatchFrontOnly: false
  });
  const [sortBy, setSortBy] = useState('score'); // score, name, elevation, difficulty
  const [useEnhancedScoring, setUseEnhancedScoring] = useState(false);
  const [patternData, setPatternData] = useState(null);
  const [communityTheories, setCommunityTheories] = useState([]);

  // Initialize trails with scores
  useEffect(() => {
    const trailsWithScores = utahTrails.map(trail => {
      const scoreBreakdown = calculateTrailScore(trail);
      return {
        ...trail,
        baseScore: scoreBreakdown.total,
        totalScore: scoreBreakdown.total,
        scoreBreakdown: scoreBreakdown,
        clueBonus: 0,
        matchedClues: [],
        enhancedScore: null,
        confidence: 0.5
      };
    });
    setTrails(trailsWithScores);
  }, []);

  // Update scores when enhanced scoring is toggled
  useEffect(() => {
    if (useEnhancedScoring && patternData) {
      updateTrailsWithEnhancedScoring();
    }
  }, [useEnhancedScoring, patternData]);

  // Apply filters and sorting
  useEffect(() => {
    let filtered = trails.filter(trail => {
      // Difficulty filter
      if (filters.difficulty !== 'all' && trail.difficulty !== filters.difficulty) {
        return false;
      }

      // Region filter (simplified - could be expanded)
      if (filters.region !== 'all') {
        if (filters.region === 'salt-lake' && !trail.location.includes('Salt Lake')) {
          return false;
        }
        if (filters.region === 'utah-county' && !trail.location.includes('American Fork')) {
          return false;
        }
      }

      // Score range filter
      if (trail.totalScore < filters.scoreRange[0] || trail.totalScore > filters.scoreRange[1]) {
        return false;
      }

      // Wasatch Front only filter
      if (filters.wasatchFrontOnly && !trail.wasatchFront) {
        return false;
      }

      return true;
    });

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'score':
          return b.totalScore - a.totalScore;
        case 'name':
          return a.name.localeCompare(b.name);
        case 'elevation':
          return b.elevation - a.elevation;
        case 'difficulty':
          const difficultyOrder = { 'Easy': 1, 'Moderate': 2, 'Hard': 3 };
          return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
        default:
          return b.totalScore - a.totalScore;
      }
    });

    setFilteredTrails(filtered);
  }, [trails, filters, sortBy]);

  const updateTrailsWithClues = (clues) => {
    if (useEnhancedScoring && patternData) {
      updateTrailsWithEnhancedScoring(clues);
    } else {
      const updatedTrails = updateScoresWithClues(trails, clues);
      setTrails(updatedTrails);
    }
  };

  const updateTrailsWithEnhancedScoring = (clues = []) => {
    enhancedScoringAlgorithm.loadPatterns(patternData);

    const enhancedTrails = trails.map(trail => {
      const enhancedScore = enhancedScoringAlgorithm.calculateEnhancedScore(
        trail,
        clues,
        communityTheories
      );

      return {
        ...trail,
        enhancedScore: enhancedScore,
        totalScore: enhancedScore.total,
        confidence: enhancedScore.confidence,
        patternMatches: enhancedScore.patterns?.breakdown || {}
      };
    });

    setTrails(enhancedTrails);
  };

  const loadPatternData = async (collectedData) => {
    try {
      const patterns = await patternRecognitionEngine.analyzePatterns(collectedData);
      setPatternData(patterns);
      return patterns;
    } catch (error) {
      console.error('Failed to load pattern data:', error);
      return null;
    }
  };

  const toggleEnhancedScoring = (enabled) => {
    setUseEnhancedScoring(enabled);
  };

  const addCommunityTheory = (theory) => {
    setCommunityTheories(prev => [...prev, {
      id: Date.now(),
      ...theory,
      addedAt: new Date().toISOString()
    }]);
  };

  const updateFilters = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const resetFilters = () => {
    setFilters({
      difficulty: 'all',
      region: 'all',
      scoreRange: [0, 100],
      wasatchFrontOnly: false
    });
  };

  const getTopTrails = (count = 5) => {
    return [...trails]
      .sort((a, b) => b.totalScore - a.totalScore)
      .slice(0, count);
  };

  const getTrailById = (id) => {
    return trails.find(trail => trail.id === id);
  };

  const value = {
    trails,
    filteredTrails,
    filters,
    sortBy,
    useEnhancedScoring,
    patternData,
    communityTheories,
    updateTrailsWithClues,
    updateFilters,
    resetFilters,
    setSortBy,
    getTopTrails,
    getTrailById,
    loadPatternData,
    toggleEnhancedScoring,
    addCommunityTheory,
    updateTrailsWithEnhancedScoring
  };

  return (
    <TrailContext.Provider value={value}>
      {children}
    </TrailContext.Provider>
  );
}
